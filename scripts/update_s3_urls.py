#!/usr/bin/env python
"""
Utility script to update existing Qdrant documents with S3 URLs
This is needed for documents that were ingested before S3 integration was added
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add parent directory to path to import utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import config
from utils.database_utils import get_mongo_client
from utils.s3_utils import upload_file_to_s3, get_s3_url
from utils.qdrant_utils import qdrant_manager

def find_documents_without_s3_url(limit: int = 100) -> List[Dict[str, Any]]:
    """
    Find documents in the database that don't have an S3 URL
    
    Args:
        limit: Maximum number of documents to return
        
    Returns:
        List of document metadata
    """
    client = get_mongo_client()
    db = client[config.database.database_name]
    collection = db[config.database.collection_name]
    
    # Find documents that don't have s3_url field or it's empty
    query = {
        "$or": [
            {"s3_url": {"$exists": False}},
            {"s3_url": ""}
        ]
    }
    
    # Return only essential fields to minimize memory usage
    projection = {
        "document_number": 1,
        "document_title": 1,
        "document_type": 1,
        "date_of_issue": 1,
        "_id": 0  # Exclude MongoDB _id
    }
    
    docs = list(collection.find(query, projection).limit(limit))
    logger.info(f"Found {len(docs)} documents without S3 URL")
    return docs

def find_pdf_file_by_document_id(doc_id: str, pdf_dirs: List[str]) -> Optional[str]:
    """
    Find PDF file by document ID in specified directories
    
    Args:
        doc_id: Document ID to search for
        pdf_dirs: List of directories to search
        
    Returns:
        Full path to PDF file if found, None otherwise
    """
    # Clean up document ID for filename matching
    clean_id = doc_id.replace("/", "_").replace("\\", "_").replace(" ", "_")
    
    # Possible filename patterns
    patterns = [
        f"{clean_id}.pdf", 
        f"{doc_id}.pdf",
        f"*{clean_id}*.pdf",
        f"*{doc_id}*.pdf"
    ]
    
    for directory in pdf_dirs:
        dir_path = Path(directory)
        if not dir_path.exists():
            logger.warning(f"Directory not found: {directory}")
            continue
        
        # Try to find file with exact name first
        for pattern in patterns[:2]:  # Try exact matches first
            for filepath in dir_path.glob(pattern):
                logger.info(f"Found exact match: {filepath}")
                return str(filepath)
        
        # Then try with glob patterns
        for pattern in patterns[2:]:  # Try glob patterns
            matches = list(dir_path.glob(pattern))
            if matches:
                logger.info(f"Found match with pattern '{pattern}': {matches[0]}")
                return str(matches[0])
    
    logger.warning(f"No PDF file found for document ID: {doc_id}")
    return None

def update_document_with_s3_url(
    doc_id: str,
    doc_type: str,
    pdf_path: str
) -> bool:
    """
    Upload PDF to S3 and update document in database with S3 URL
    
    Args:
        doc_id: Document ID
        doc_type: Document type (used for collection name)
        pdf_path: Path to PDF file
        
    Returns:
        Success status
    """
    try:
        # Determine collection name from document type
        from utils.document_pipeline import DocumentType, DocumentTypeMapper
        
        try:
            doc_type_enum = DocumentType(doc_type)
        except ValueError:
            doc_type_enum = DocumentType.OTHER
            
        collection_name = DocumentTypeMapper.document_type_to_collection_name(doc_type_enum)
        
        # Upload to S3
        s3_key = f"documents/{collection_name}/{doc_id}.pdf"
        upload_result = upload_file_to_s3(
            local_file_path=pdf_path,
            s3_key=s3_key,
            metadata={
                "document_id": doc_id,
                "collection": collection_name,
                "document_type": str(doc_type)
            }
        )
        
        if not upload_result["success"]:
            logger.error(f"Failed to upload {pdf_path} to S3: {upload_result.get('error')}")
            return False
            
        s3_url = upload_result["s3_url"]
        logger.info(f"Uploaded {pdf_path} to {s3_url}")
        
        # Update database
        client = get_mongo_client()
        db = client[config.database.database_name]
        collection = db[config.database.collection_name]
        
        result = collection.update_one(
            {"document_number": doc_id},
            {"$set": {"s3_url": s3_url}}
        )
        
        if result.modified_count > 0:
            logger.info(f"Updated database record for {doc_id}")
        else:
            logger.warning(f"No database records updated for {doc_id}")
            
        # Update Qdrant
        success = update_qdrant_records_with_s3_url(doc_id, collection_name, s3_url)
        if success:
            logger.info(f"Updated Qdrant records for {doc_id}")
        else:
            logger.warning(f"Failed to update Qdrant records for {doc_id}")
            
        return True
        
    except Exception as e:
        logger.error(f"Error updating document {doc_id}: {e}")
        return False

def update_qdrant_records_with_s3_url(doc_id: str, collection_name: str, s3_url: str) -> bool:
    """
    Update all Qdrant records for a document with S3 URL
    
    Args:
        doc_id: Document ID
        collection_name: Qdrant collection name
        s3_url: S3 URL to add
        
    Returns:
        Success status
    """
    try:
        from utils.qdrant_utils import generate_valid_point_id
        
        # Generate valid point ID
        valid_point_id = generate_valid_point_id(doc_id)
        
        # Find all points for this document in Qdrant
        search_result = qdrant_manager.client.search(
            collection_name=collection_name,
            query_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="metadata.document_number",
                        match=models.MatchValue(value=doc_id)
                    )
                ]
            ),
            limit=100
        )
        
        if not search_result:
            logger.warning(f"No Qdrant records found for {doc_id}")
            return False
        
        success = True
        for point in search_result:
            point_id = point.id
            payload = point.payload
            
            # Add S3 URL to metadata if it exists
            if "metadata" in payload:
                payload["metadata"]["s3_url"] = s3_url
            
            # Update the point
            try:
                qdrant_manager.client.set_payload(
                    collection_name=collection_name,
                    payload={"metadata.s3_url": s3_url},
                    points=[point_id]
                )
            except Exception as e:
                logger.error(f"Failed to update Qdrant point {point_id}: {e}")
                success = False
                
        return success
        
    except Exception as e:
        logger.error(f"Error updating Qdrant records for {doc_id}: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Update existing documents with S3 URLs')
    parser.add_argument('--limit', type=int, default=100, help='Maximum number of documents to process')
    parser.add_argument('--pdf-dirs', type=str, required=True, help='Comma-separated list of directories containing PDF files')
    args = parser.parse_args()
    
    # Parse PDF directories
    pdf_dirs = [dir.strip() for dir in args.pdf_dirs.split(',')]
    
    # Find documents without S3 URL
    docs = find_documents_without_s3_url(limit=args.limit)
    
    # Process each document
    success_count = 0
    for doc in docs:
        doc_id = doc.get('document_number')
        doc_type = doc.get('document_type')
        doc_title = doc.get('document_title', 'Unknown Title')
        
        logger.info(f"Processing: {doc_id} - {doc_title}")
        
        # Find PDF file
        pdf_path = find_pdf_file_by_document_id(doc_id, pdf_dirs)
        if not pdf_path:
            logger.warning(f"No PDF file found for {doc_id}")
            continue
        
        # Update document with S3 URL
        success = update_document_with_s3_url(doc_id, doc_type, pdf_path)
        if success:
            success_count += 1
    
    logger.info(f"Updated {success_count} out of {len(docs)} documents with S3 URLs")

if __name__ == "__main__":
    # Add import here to avoid circular imports
    from qdrant_client import models
    
    # Check if AWS credentials are set
    if not config.s3.access_key_id or not config.s3.secret_access_key:
        logger.error("AWS credentials not set. Please run 'source set_aws_env.sh' first.")
        sys.exit(1)
    
    main()
