#!/usr/bin/env python3
"""
Test script to verify that removal actions are working properly.
This script tests the removal logic with different types of document IDs.
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_removal_actions.log')
    ]
)
logger = logging.getLogger(__name__)

def test_removal_handler():
    """Test the improved removal handler"""
    logger.info("🧪 Testing improved removal handler...")
    
    try:
        from improved_removal_handler import handle_special_document_ids, normalize_document_id
        
        # Test cases
        test_cases = [
            ("RBI/FED/2024-25/17", True, "Valid RBI document ID"),
            ("MANUAL_REVIEW_REQUIRED", True, "Should now allow with warning"),
            ("DBR.No.Ret.BC.78/12.02.001/2024-25", True, "Valid DBR document ID"),
            ("PRESERVE_DOCUMENT", False, "Should reject preserved documents"),
            ("TEST_123", False, "Should reject test documents"),
            ("", False, "Should reject empty document ID"),
        ]
        
        for doc_id, expected_valid, description in test_cases:
            is_valid, reason = handle_special_document_ids(doc_id)
            status = "✅ PASS" if is_valid == expected_valid else "❌ FAIL"
            logger.info(f"{status} {description}: '{doc_id}' -> valid={is_valid}, reason='{reason}'")
        
        # Test normalization
        logger.info("\n🔧 Testing document ID normalization...")
        norm_cases = [
            ("RBI/FED/2024-25/17/", "RBI/FED/2024-25/17"),
            ("RBI\\FED\\2024-25\\17", "RBI/FED/2024-25/17"),
            ("RBI/FED/2024-25/17 ", "RBI/FED/2024-25/17"),
        ]
        
        for original, expected in norm_cases:
            normalized = normalize_document_id(original)
            status = "✅ PASS" if normalized == expected else "❌ FAIL"
            logger.info(f"{status} '{original}' -> '{normalized}' (expected: '{expected}')")
            
    except ImportError as e:
        logger.error(f"❌ Could not import removal handler: {e}")
        return False
    
    return True

def test_kb_executor_removal():
    """Test the knowledge base executor removal logic"""
    logger.info("\n🧪 Testing KB executor removal logic...")
    
    try:
        # Set up environment
        os.environ['OPENAI_API_KEYS'] = 'test-key'
        
        from utils.knowledge_base_update_executor import KnowledgeBaseUpdateExecutor
        
        executor = KnowledgeBaseUpdateExecutor()
        
        # Test removal action with real document ID
        test_action = {
            'action_type': 'REMOVE_DOCUMENT',
            'target_document': 'RBI/FED/2023-24/15',
            'collection_name': ['rbi_circular'],
            'use_vector_search': True,
            'document_id': 'RBI/FED/2023-24/15',
            'document_number': 'Test Document Number'
        }
        
        notification_data = {
            'Title': 'Test Notification for Removal',
            'Date': '2024-01-01'
        }
        
        logger.info("🎯 Testing removal action with real document ID...")
        logger.info(f"Action: {test_action}")

        # This should now work with the fixed vector search logic
        result = executor._remove(test_action, notification_data)

        logger.info(f"Result: {result}")

        # Check if the action was processed (success or failure is OK, as long as it's not rejected)
        if result.get('action') == 'REMOVE_DOCUMENT':
            logger.info("✅ Removal action processed successfully")
            return True
        else:
            logger.error(f"❌ Removal action not processed correctly: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing KB executor removal: {e}")
        return False

def test_action_generation():
    """Test that removal actions are being generated properly"""
    logger.info("\n🧪 Testing removal action generation...")
    
    try:
        # Mock notification data that should generate removal actions
        mock_notification = {
            'Title': 'Amendment to Guidelines on Risk Management and Inter-Bank Dealings',
            'Description': 'This circular supersedes the earlier circular RBI/FED/2023-24/15 dated March 15, 2023',
            'Date': '2024-01-15'
        }
        
        # Test if we can extract document IDs from content
        content = mock_notification['Description']
        
        import re
        # Look for RBI document patterns
        rbi_pattern = r'RBI/[A-Z]+/\d{4}-\d{2}/\d+'
        matches = re.findall(rbi_pattern, content)
        
        if matches:
            logger.info(f"✅ Found RBI document IDs in content: {matches}")
            for match in matches:
                logger.info(f"   📋 Extracted document ID: {match}")
            return True
        else:
            logger.warning("⚠️ No RBI document IDs found in test content")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing action generation: {e}")
        return False

def main():
    """Run all removal action tests"""
    logger.info("🚀 Starting removal action tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Removal Handler", test_removal_handler),
        ("KB Executor Removal", test_kb_executor_removal),
        ("Action Generation", test_action_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status} {test_name}")
        except Exception as e:
            logger.error(f"❌ FAILED {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Removal actions should be working.")
    else:
        logger.warning("⚠️ Some tests failed. Check the logs for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
