# RBI Document Updation Pipeline

## Metadata Vector Search Integration

The updation pipeline now includes a metadata vector search feature that enhances document matching and handling of LLM-generated actions.

### Key Features

- Vector-based search for document metadata using SentenceTransformer embeddings
- Improved document matching for updates, removals, and supersession actions
- Fallback to traditional search methods when vector search is not available
- Search across multiple fields (document_id, document_number, short_summary)

### Usage

```python
# Basic usage in pipeline
pipeline = RealPipelineTest()

# Search by document ID
results = pipeline.search_document_by_metadata("document_id", "DNBR.PD.004/03.10.119/2016-17")

# Find similar documents
similar_docs = pipeline.find_similar_documents("DNBR.PD.004/03.10.119/2016-17")

# Find potentially superseded documents
superseded_docs = pipeline.find_superseded_documents(
    "DNBR.PD.004/03.10.119/2016-17", 
    "DNBR.PD.004"
)

# The knowledge base update executor will automatically use 
# metadata vector search when processing LLM-generated actions
```

### Components

1. `utils/metadata_vector_utils.py` - Core implementation of the metadata vector search
2. Integration in `RealPipelineTest` for direct access to search functionality
3. Enhanced `KnowledgeBaseUpdateExecutor` that uses vector search for better document matching

### Testing

Run the test script to verify the metadata vector search functionality:

```bash
python test_metadata_search.py "Your document ID or number"
```

### Dependencies

- sentence-transformers
- qdrant-client

Make sure these are installed via pip:

```bash
pip install -r requirements.txt
```

## Configuration

The metadata vector search connects to Qdrant at http://localhost:6333 by default and uses the "metadata_vectors" collection. These can be configured in the `utils/metadata_vector_utils.py` file.

## OpenAI Structured Outputs Integration

The updation pipeline now uses OpenAI's structured outputs feature to ensure consistent, validated responses from LLM calls, which improves pipeline reliability and data quality.

### Key Features

- JSON Schema validation for all LLM-generated actions
- Structured responses for document classification, indexing decisions, and KB actions
- Improved error handling for malformed LLM responses
- Type-safe processing of LLM decisions

### Implementation Details

- Uses `response_format={"type": "json_object", "schema": schema}` with OpenAI API
- Defines clear JSON schemas for each type of decision:
  - Document classification schema
  - KB action schema
  - Indexing decision schema
- Ensures consistent table/collection targeting through schema-enforced enum values
