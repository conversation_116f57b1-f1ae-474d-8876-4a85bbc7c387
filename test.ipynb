{"cells": [{"cell_type": "code", "execution_count": 6, "id": "652bc4b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point ID: 022488a2-460c-456a-a6ad-52fddf0d9f27\n", "Document ID: RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17\n", "Similarity Score: 0.7767\n", "----------------------------------------\n", "Point ID: 0006a8de-dc2b-4468-a69c-0a2bbe93e4a7\n", "Document ID: RBI/DNBR/2016-17/42 Master Direction DNBR.PD.004/03.10.119/2016-17\n", "Similarity Score: 0.7767\n", "----------------------------------------\n", "Point ID: 00c4d689-4c79-4f7f-b53b-f5d5e0439c13\n", "Document ID: RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17\n", "Similarity Score: 0.7767\n", "----------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/h5/zs00jbpd1v19vf00cjv46w7c0000gn/T/ipykernel_73047/1358360799.py:13: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  hits = client.search(\n"]}], "source": ["from sentence_transformers import SentenceTransformer\n", "from qdrant_client import QdrantClient\n", "\n", "QDRANT_URL = \"http://localhost:6333\"\n", "COLLECTION = \"metadata_vectors\"\n", "\n", "# Initialize embedding model & Qdrant client\n", "model = SentenceTransformer(\"all-MiniLM-L6-v2\")\n", "client = QdrantClient(QDRANT_URL)\n", "\n", "def search_named_vector(collection_name, field, query_text, top_k=5):\n", "    query_embedding = model.encode(query_text).tolist()\n", "    hits = client.search(\n", "        collection_name=collection_name,\n", "        query_vector=(f\"{field}_vec\", query_embedding),\n", "        limit=top_k,\n", "        with_payload=True,\n", "        with_vectors=False\n", "    )\n", "    return hits\n", "\n", "# Example usage:\n", "if __name__ == \"__main__\":\n", "    query_text = \" DNBR.PD.004/03.10.119/2016-17\"\n", "    field = \"document_id\"  # This can also be: document_id, short_summary, document_number\n", "\n", "    results = search_named_vector(COLLECTION, field, query_text, top_k=3)\n", "\n", "    for hit in results:\n", "        print(f\"Point ID: {hit.id}\")\n", "        print(f\"Document ID: {hit.payload.get('document_id')}\")\n", "        print(f\"Similarity Score: {hit.score:.4f}\")\n", "        print(\"-\" * 40)\n"]}, {"cell_type": "code", "execution_count": null, "id": "624e6072", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "selkea_be", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}