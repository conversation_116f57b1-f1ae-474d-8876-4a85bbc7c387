# Refactored document processing with proper methodology
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class DocumentExtractionResult:
    document_actions: List[Dict]
    processing_notes: str
    requires_manual_review: bool = False

class DocumentProcessor:
    """Handles document extraction with single responsibility"""
    
    def __init__(self, llm_manager):
        self.llm_manager = llm_manager
    
    def extract_affected_documents(self, title: str, content: str, category: str, 
                                 notification_codes: dict = None, chunks: list = None) -> DocumentExtractionResult:
        """Main orchestrator - delegates to focused methods"""
        try:
            # Extract from main content
            main_actions = self._extract_from_main_content(title, content, category, notification_codes)
            
            # Extract from chunks if available
            chunk_actions = []
            if chunks:
                chunk_actions = self._extract_from_chunks(chunks, title, category, notification_codes)
            
            # Deduplicate and merge
            all_actions = main_actions + chunk_actions
            deduplicated_actions = self._deduplicate_actions(all_actions)
            
            return DocumentExtractionResult(
                document_actions=deduplicated_actions,
                processing_notes=f"Processed {len(chunks) if chunks else 0} chunks, found {len(all_actions)} actions, deduplicated to {len(deduplicated_actions)}"
            )
            
        except LLMError as e:
            logger.error(f"LLM error in document extraction: {e}")
            return self._get_fallback_result()
        except Exception as e:
            logger.error(f"Error extracting documents: {e}", exc_info=True)
            return self._get_fallback_result()
    
    def _extract_from_main_content(self, title: str, content: str, category: str, codes: dict) -> List[Dict]:
        """Extract documents from main notification content"""
        prompt = self._build_extraction_prompt(title, content, category, codes)
        result = self.llm_manager.make_llm_call(prompt, AffectedDocumentsResult)
        return result.get('document_actions', [])
    
    def _extract_from_chunks(self, chunks: List, title: str, category: str, codes: dict) -> List[Dict]:
        """Extract documents from PDF chunks"""
        chunk_actions = []
        for idx, chunk in enumerate(chunks):
            if not self._is_valid_chunk(chunk):
                continue
                
            try:
                actions = self._process_single_chunk(chunk, idx, title, category, codes)
                chunk_actions.extend(actions)
            except Exception as e:
                logger.warning(f"Failed to process chunk {idx+1}: {e}")
                continue
                
        return chunk_actions
    
    def _process_single_chunk(self, chunk: Dict, idx: int, title: str, category: str, codes: dict) -> List[Dict]:
        """Process a single chunk for document extraction"""
        chunk_content = chunk.get('content', '')
        if not chunk_content.strip():
            return []
            
        prompt = self._build_extraction_prompt(f"{title} (Chunk {idx+1})", chunk_content, category, codes)
        result = self.llm_manager.make_llm_call(prompt, AffectedDocumentsResult)
        
        # Enrich with chunk metadata
        actions = result.get('document_actions', [])
        for action in actions:
            action['source_chunk'] = {'index': idx, 'content_preview': chunk_content[:100]}
            
        return actions
    
    def _deduplicate_actions(self, actions: List[Dict]) -> List[Dict]:
        """Remove duplicate actions using smart merging"""
        action_groups = {}
        
        for action in actions:
            key = (action.get('target_document', ''), action.get('action_required', ''))
            
            if key in action_groups:
                # Merge URLs and reasoning
                existing = action_groups[key]
                self._merge_action_data(existing, action)
            else:
                action_groups[key] = action.copy()
        
        return list(action_groups.values())
    
    def _merge_action_data(self, existing: Dict, new_action: Dict) -> None:
        """Merge data from new action into existing"""
        # Merge URLs if missing
        for url_field in ['new_document_url', 'rbi_page_url']:
            if new_action.get(url_field) and not existing.get(url_field):
                existing[url_field] = new_action[url_field]
        
        # Combine reasoning
        if new_action.get('reasoning'):
            existing_reasons = set(existing.get('reasoning', '').split('. '))
            new_reasons = set(new_action['reasoning'].split('. '))
            existing['reasoning'] = '. '.join(existing_reasons.union(new_reasons))
    
    def _is_valid_chunk(self, chunk) -> bool:
        """Check if chunk is valid for processing"""
        return isinstance(chunk, dict) and chunk.get('content', '').strip()
    
    def _build_extraction_prompt(self, title: str, content: str, category: str, codes: dict) -> str:
        """Build LLM prompt for document extraction"""
        return f"""
        Extract affected documents from this RBI notification:
        
        Title: {title}
        Category: {category}
        Content: {content[:2000]}
        Codes: {codes}
        
        Focus on supersession detection and document relationships.
        """
    
    def _get_fallback_result(self) -> DocumentExtractionResult:
        """Return fallback when extraction fails"""
        return DocumentExtractionResult(
            document_actions=[],
            processing_notes="Extraction failed - manual review required",
            requires_manual_review=True
        )

class ActionDeterminer:
    """Determines KB update actions from extracted documents"""
    
    def __init__(self, llm_manager):
        self.llm_manager = llm_manager
    
    def determine_update_actions(self, title: str, category: str, affected_documents: List, 
                               content: str, notification_data: dict = None) -> Dict:
        """Determine specific KB update actions"""
        try:
            # Truncate content to prevent token limits
            truncated_content = self._truncate_content(content)
            
            # Generate actions via LLM
            actions = self._generate_actions_via_llm(title, category, affected_documents, truncated_content)
            
            # Validate and enhance each action
            validated_actions = []
            for action in actions:
                validated_action = self._validate_action(action, notification_data)
                validated_actions.append(validated_action)
            
            return {
                'actions': validated_actions,
                'processing_notes': f"Generated {len(validated_actions)} validated actions"
            }
            
        except Exception as e:
            logger.error(f"Error determining actions: {e}")
            return self._get_simplified_actions(title, category, affected_documents)
    
    def _truncate_content(self, content: str, max_length: int = 8000) -> str:
        """Truncate content to prevent token limit issues"""
        if len(content) <= max_length:
            return content
        
        logger.warning(f"Content truncated from {len(content)} to {max_length} chars")
        return content[:max_length] + "\n\n[CONTENT TRUNCATED]"
    
    def _generate_actions_via_llm(self, title: str, category: str, docs: List, content: str) -> List[Dict]:
        """Generate actions using LLM"""
        prompt = f"""
        Determine KB actions for: {title}
        Category: {category}
        Affected docs: {len(docs)}
        Content: {content[:1000]}
        
        Return 3-5 specific actions with target_document, action_type, details.
        """
        
        result = self.llm_manager.make_llm_call(prompt, UpdateActionResult)
        return result.get('actions', [])
    
    def _validate_action(self, action: Dict, notification_data: dict) -> Dict:
        """Validate and enhance a single action"""
        # Ensure required fields
        if not action.get('target_document') and action.get('action_type') == 'REMOVE_DOCUMENT':
            action['target_document'] = self._extract_document_id(action.get('details', ''))
        
        # Add URLs from notification data
        if notification_data and notification_data.get('PDF Link'):
            action.setdefault('new_document_url', notification_data['PDF Link'])
        
        return action
    
    def _extract_document_id(self, details: str) -> str:
        """Extract document ID from action details"""
        import re
        patterns = [r'RBI/[A-Z]+/\d{4}-\d{2}/\d+', r'[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}']
        
        for pattern in patterns:
            match = re.search(pattern, details, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return 'MANUAL_REVIEW_REQUIRED'
    
    def _get_simplified_actions(self, title: str, category: str, docs: List) -> Dict:
        """Generate simplified actions when LLM fails"""
        actions = []
        
        # Basic rule-based actions
        if 'withdrawal' in category.lower():
            actions.append({
                'action_type': 'REMOVE_DOCUMENT',
                'target_document': 'MANUAL_REVIEW_REQUIRED',
                'details': f'Document withdrawal based on {category}',
                'priority': 'HIGH'
            })
        else:
            actions.append({
                'action_type': 'ADD_DOCUMENT',
                'target_document': 'MANUAL_REVIEW_REQUIRED', 
                'details': f'New document for {category}',
                'priority': 'MEDIUM'
            })
        
        return {
            'actions': actions,
            'processing_notes': 'Simplified rule-based actions',
            'requires_manual_review': True
        }