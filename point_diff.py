import requests
from typing import List, Dict, Tuple

QDRANT_COLLECTIONS = [
    "rbi_master_direction",
    "rbi_master_circular",
    "rbi_circular",
    "rbi_other"
]

LOCAL_QDRANT_URL = "http://localhost:6333"
REMOTE_QDRANT_URL = "http://localhost:6333"

LIMIT = 10000  # adjust based on expected collection size


def fetch_documents(base_url: str, collection: str) -> List[Dict[str, str]]:
    url = f"{base_url}/collections/{collection}/points/scroll"
    payload = {
        "limit": LIMIT,
        "with_payload": True,
        "with_vector": False,
        "filter": {
            "must": []
        }
    }
    
    response = requests.post(url, json=payload)
    response.raise_for_status()
    data = response.json()

    points = data.get("result", {}).get("points", [])
    return [
        {
            "document_title": point["payload"].get("document_title"),
            "chunk_index": point["payload"].get("chunk_index")
        }
        for point in points if "payload" in point
    ]


def compare_documents(
    local_docs: List[Dict[str, str]],
    remote_docs: List[Dict[str, str]]
) -> Tuple[List[Dict[str, str]], List[Dict[str, str]]]:
    local_set = set((doc['document_title'], doc['chunk_index']) for doc in local_docs)
    remote_set = set((doc['document_title'], doc['chunk_index']) for doc in remote_docs)

    only_in_local = local_set - remote_set
    only_in_remote = remote_set - local_set

    return list(only_in_local), list(only_in_remote)


def main():
    for collection in QDRANT_COLLECTIONS:
        print(f"\nComparing collection: {collection}")

        try:
            local_docs = fetch_documents(LOCAL_QDRANT_URL, collection)
            remote_docs = fetch_documents(REMOTE_QDRANT_URL, collection)

            only_local, only_remote = compare_documents(local_docs, remote_docs)

            print(f"Only in local ({len(only_local)}):", only_local[:10])
            print(f"Only in remote ({len(only_remote)}):", only_remote[:10])
        except Exception as e:
            print(f"Error processing collection '{collection}': {e}")


if __name__ == "__main__":
    main()
