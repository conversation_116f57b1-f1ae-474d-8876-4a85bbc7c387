from qdrant_client import QdrantClient
import os
import requests
import time

# 🔧 Config
QDRANT_URL = "http://localhost:6333"
API_KEY = "YOUR_API_KEY"  # Leave as "" if not needed
BACKUP_DIR = os.path.expanduser("~/dev backup")
TIMEOUT = 120.0  # ⏱️ Timeout in seconds (not httpx.Timeout)

# 🔌 Connect to Qdrant
client = QdrantClient(url=QDRANT_URL, api_key=API_KEY, timeout=TIMEOUT)
os.makedirs(BACKUP_DIR, exist_ok=True)

# 📦 Get all collections
collections = client.get_collections().collections

for coll in collections:
    name = coll.name
    try:
        print(f"📤 Creating snapshot for '{name}'...")
        snapshot_info = client.create_snapshot(collection_name=name, wait=True)
        snapshot_name = snapshot_info.name
        print(f"✅ Created: {snapshot_name}")

        # 🌐 Build snapshot URL
        snapshot_url = f"{QDRANT_URL}/collections/{name}/snapshots/{snapshot_name}"
        local_path = os.path.join(BACKUP_DIR, f"{name}__{snapshot_name}")
        headers = {"api-key": API_KEY} if API_KEY else {}

        # 📥 Download snapshot
        print(f"⬇️ Downloading to: {local_path}")
        response = requests.get(snapshot_url, headers=headers, timeout=TIMEOUT)

        if response.status_code == 200:
            with open(local_path, "wb") as f:
                f.write(response.content)
            print(f"✅ Saved: {local_path}")
        else:
            print(f"❌ Download failed: {response.status_code} - {response.text}")

    except Exception as e:
        print(f"⚠️ Error for collection '{name}': {e}")
    print("-" * 60)
    time.sleep(1)  # small delay for safety
