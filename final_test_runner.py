#!/usr/bin/env python3
"""
Final Test Runner - Guaranteed to work with proper IDs and titles
"""

import json
import os
import sys
import requests
import tempfile
import re
from datetime import datetime
from pathlib import Path

class FinalPipelineTest:
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEYS', '').split('|')[0] or os.getenv('OPENAI_API_KEY')
        if not self.openai_api_key:
            raise ValueError("Need OPENAI_API_KEY")
    
    def get_pdf_text(self, pdf_url):
        """Download PDF and extract text"""
        try:
            import fitz
            response = requests.get(pdf_url, timeout=30)
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
                f.write(response.content)
                temp_path = f.name
            
            doc = fitz.open(temp_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            os.unlink(temp_path)
            return text
        except:
            return ""
    
    def extract_ids(self, text):
        """Extract document IDs from text"""
        patterns = [
            r'DBOD\.No\.[A-Z]+\.[A-Z]+\.\d+/[A-Z0-9\.\-\(\)]+',
            r'RBI/\d{4}-\d{2}/\d+',
            r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}'
        ]
        
        ids = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            ids.extend(matches)
        
        return list(set(ids))
    
    def get_kb_actions(self, title, pdf_text, doc_ids):
        """Get KB actions with specific IDs"""
        try:
            import openai
            client = openai.OpenAI(api_key=self.openai_api_key)
            
            prompt = f"""
Title: {title}
PDF Content: {pdf_text[:3000]}
Document IDs Found: {doc_ids}

Extract KB actions with SPECIFIC document IDs and titles from the content.

Return JSON:
{{
    "actions": [
        {{
            "action_type": "REMOVE_DOCUMENT|ADD_DOCUMENT|UPDATE_DOCUMENT",
            "target_document": "specific ID from content",
            "target_title": "specific title from content", 
            "collection": "rbi_circular",
            "priority": "HIGH",
            "reasoning": "why this action"
        }}
    ]
}}
"""
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            return json.loads(response.choices[0].message.content)
        except:
            return {"actions": []}

def run_test(category):
    """Run test for category"""
    test_file = Path(f"pipeline_tests/{category}.json")
    with open(test_file) as f:
        tests = json.load(f)
    
    pipeline = FinalPipelineTest()
    results = []
    
    for i, test in enumerate(tests[:2]):
        print(f"\n=== Test {i+1}: {test['title'][:50]}... ===")
        
        # Get PDF content
        pdf_text = pipeline.get_pdf_text(test['pdf_link'])
        doc_ids = pipeline.extract_ids(pdf_text)
        
        print(f"PDF Length: {len(pdf_text)} chars")
        print(f"Document IDs Found: {len(doc_ids)}")
        for doc_id in doc_ids[:5]:
            print(f"  - {doc_id}")
        
        # Get KB actions
        kb_result = pipeline.get_kb_actions(test['title'], pdf_text, doc_ids)
        actions = kb_result.get('actions', [])
        
        print(f"KB Actions: {len(actions)}")
        for j, action in enumerate(actions):
            print(f"  {j+1}. {action.get('action_type')} -> {action.get('target_document')}")
            print(f"     Title: {action.get('target_title', '')[:60]}...")
        
        results.append({
            'test': i+1,
            'title': test['title'],
            'expected': test['action'],
            'pdf_length': len(pdf_text),
            'doc_ids_found': doc_ids,
            'kb_actions': actions
        })
    
    # Save results
    output_file = f"final_results_{category}_{datetime.now().strftime('%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n✅ Results saved to: {output_file}")
    return results

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python final_test_runner.py <category>")
        sys.exit(1)
    
    run_test(sys.argv[1])