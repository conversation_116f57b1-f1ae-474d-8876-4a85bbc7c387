#!/usr/bin/env python3
"""
Single Category Test Runner

Run tests for a single category to verify KB actions manually.
Usage: python test_single_category.py <category_name>
"""

import json
import logging
import sys
from datetime import datetime
from pathlib import Path
import traceback

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from manual_test import RealPipelineTest

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_single_category_test(category_name: str, max_tests: int = 3):
    """Run tests for a single category with limited number of tests"""
    
    # Initialize pipeline
    logger.info("🚀 Initializing pipeline...")
    try:
        pipeline = RealPipelineTest()
        logger.info("✅ Pipeline initialized successfully")
    except Exception as e:
        logger.error(f"❌ Failed to initialize pipeline: {e}")
        return
    
    # Load test file
    test_file = Path(f"pipeline_tests/{category_name}.json")
    if not test_file.exists():
        logger.error(f"❌ Test file not found: {test_file}")
        return
    
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    # Limit number of tests
    test_data = test_data[:max_tests]
    logger.info(f"📁 Running {len(test_data)} tests from {category_name}")
    
    # Create output directory
    output_dir = Path(f"test_outputs_{category_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    output_dir.mkdir(exist_ok=True)
    
    results = []
    
    for i, notification in enumerate(test_data):
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 Test {i+1}/{len(test_data)}: {notification.get('title', 'Unknown')[:50]}...")
        
        try:
            # Create notification data
            notification_data = {
                'Title': notification.get('title', ''),
                'PDF Link': notification.get('pdf_link', ''),
                'Link': notification.get('pdf_link', ''),
                'Date': datetime.now().strftime('%Y-%m-%d'),
                'notification_id': notification.get('notification_id', ''),
                'expected_action': notification.get('action', ''),
                'summary': notification.get('summary', '')
            }
            
            # Step 1: Analyze notification
            logger.info("📊 Analyzing notification...")
            analysis_result = pipeline.notification_processor.analyze_notification(
                title=notification_data['Title'],
                rss_description=notification_data['summary'],
                link=notification_data['PDF Link']
            )
            
            # Step 2: Extract affected documents
            logger.info("🔍 Extracting affected documents...")
            affected_docs_result = pipeline.notification_processor.extract_affected_documents(
                title=notification_data['Title'],
                content=notification_data['summary'],
                category=analysis_result.get('category', ''),
                notification_codes={},
                chunks=[]
            )
            
            # Step 3: Determine update actions
            logger.info("🎯 Determining update actions...")
            update_actions_result = pipeline.notification_processor.determine_update_actions(
                title=notification_data['Title'],
                category=analysis_result.get('category', ''),
                affected_documents=affected_docs_result.get('document_actions', []),
                rss_description=notification_data['summary'],
                notification_data=notification_data
            )
            
            # Compile result
            result = {
                'test_index': i + 1,
                'notification_data': notification_data,
                'analysis_result': analysis_result,
                'affected_documents': affected_docs_result,
                'update_actions': update_actions_result,
                'status': 'success',
                'processing_timestamp': datetime.now().isoformat()
            }
            
            # Extract KB actions for verification
            kb_actions = []
            
            # From update actions
            for action in update_actions_result.get('actions', []):
                kb_actions.append({
                    'source': 'update_actions',
                    'action_type': action.get('action_type', ''),
                    'target_document': action.get('target_document', ''),
                    'details': action.get('details', ''),
                    'priority': action.get('priority', ''),
                    'new_document_url': action.get('new_document_url', ''),
                    'rbi_page_url': action.get('rbi_page_url', '')
                })
            
            # From affected documents
            for action in affected_docs_result.get('document_actions', []):
                kb_actions.append({
                    'source': 'affected_documents',
                    'action_type': action.get('action_required', ''),
                    'target_document': action.get('target_document', ''),
                    'document_type': action.get('document_type', ''),
                    'type_of_impact': action.get('type_of_impact', ''),
                    'collection': action.get('collection', ''),
                    'reasoning': action.get('reasoning', '')
                })
            
            result['kb_actions'] = kb_actions
            results.append(result)
            
            # Save individual result
            individual_file = output_dir / f"test_{i+1}_{notification.get('notification_id', 'unknown')}.json"
            with open(individual_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ Test {i+1} completed successfully")
            logger.info(f"📋 Found {len(kb_actions)} KB actions")
            
            # Print summary for manual verification
            print(f"\n📋 MANUAL VERIFICATION - Test {i+1}:")
            print(f"Title: {notification_data['Title']}")
            print(f"Expected Action: {notification.get('action', 'N/A')}")
            print(f"LLM Category: {analysis_result.get('category', 'N/A')}")
            print(f"KB Actions Found: {len(kb_actions)}")
            
            for j, action in enumerate(kb_actions):
                print(f"  Action {j+1}: {action.get('action_type', 'N/A')} -> {action.get('target_document', 'N/A')}")
            
        except Exception as e:
            logger.error(f"❌ Test {i+1} failed: {e}")
            logger.error(traceback.format_exc())
            
            result = {
                'test_index': i + 1,
                'notification_data': notification_data,
                'error': str(e),
                'error_traceback': traceback.format_exc(),
                'status': 'error',
                'processing_timestamp': datetime.now().isoformat()
            }
            results.append(result)
    
    # Save summary
    summary = {
        'category': category_name,
        'total_tests': len(test_data),
        'successful_tests': len([r for r in results if r.get('status') == 'success']),
        'failed_tests': len([r for r in results if r.get('status') == 'error']),
        'total_kb_actions': sum(len(r.get('kb_actions', [])) for r in results),
        'results': results,
        'processing_timestamp': datetime.now().isoformat()
    }
    
    summary_file = output_dir / "summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
    
    # Generate verification report
    report_file = output_dir / "VERIFICATION_REPORT.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"# {category_name.title()} Test Verification Report\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Summary\n\n")
        f.write(f"- **Total Tests:** {summary['total_tests']}\n")
        f.write(f"- **Successful:** {summary['successful_tests']}\n")
        f.write(f"- **Failed:** {summary['failed_tests']}\n")
        f.write(f"- **Total KB Actions:** {summary['total_kb_actions']}\n\n")
        
        f.write("## Test Results\n\n")
        for result in results:
            if result.get('status') == 'success':
                f.write(f"### ✅ Test {result['test_index']}\n")
                f.write(f"**Title:** {result['notification_data']['Title']}\n")
                f.write(f"**Expected Action:** {result['notification_data']['expected_action']}\n")
                f.write(f"**LLM Category:** {result['analysis_result'].get('category', 'N/A')}\n")
                f.write(f"**KB Actions:** {len(result.get('kb_actions', []))}\n\n")
                
                for i, action in enumerate(result.get('kb_actions', [])):
                    f.write(f"**Action {i+1}:** {action.get('action_type', 'N/A')} -> {action.get('target_document', 'N/A')}\n")
                f.write("\n")
            else:
                f.write(f"### ❌ Test {result['test_index']} (FAILED)\n")
                f.write(f"**Error:** {result.get('error', 'Unknown error')}\n\n")
    
    logger.info(f"\n🎉 Test run completed!")
    logger.info(f"📊 Results: {summary['successful_tests']}/{summary['total_tests']} successful")
    logger.info(f"📁 Output saved to: {output_dir}")
    logger.info(f"📋 Review {report_file} for manual verification")

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_single_category.py <category_name>")
        print("Available categories:")
        test_dir = Path("pipeline_tests")
        for test_file in test_dir.glob("*.json"):
            print(f"  - {test_file.stem}")
        sys.exit(1)
    
    category_name = sys.argv[1]
    run_single_category_test(category_name)

if __name__ == "__main__":
    main()