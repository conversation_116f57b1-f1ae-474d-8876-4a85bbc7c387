#!/usr/bin/env python3
"""
Enhanced Test Runner with PDF Content Analysis

Downloads PDFs and extracts specific document IDs and titles.
"""

import json
import logging
import os
import sys
import requests
import tempfile
import re
from datetime import datetime
from pathlib import Path
import traceback

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedPipelineTest:
    """Enhanced pipeline test that extracts actual document IDs from PDFs"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_API_KEYS', '').split('|')[0]
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
    
    def download_and_extract_pdf_text(self, pdf_url: str) -> str:
        """Download PDF and extract text content"""
        try:
            import fitz  # PyMuPDF
            
            # Download PDF
            response = requests.get(pdf_url, timeout=30)
            response.raise_for_status()
            
            # Save to temp file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                temp_file.write(response.content)
                temp_path = temp_file.name
            
            # Extract text
            doc = fitz.open(temp_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            
            # Clean up
            os.unlink(temp_path)
            
            return text
            
        except Exception as e:
            logger.error(f"PDF extraction failed: {e}")
            return ""
    
    def extract_document_codes(self, text: str) -> dict:
        """Extract RBI document codes from text"""
        codes = {
            'rbi_codes': [],
            'circular_numbers': [],
            'department_codes': [],
            'all_matches': []
        }
        
        patterns = [
            r'RBI/\d{4}-\d{2}/\d+',
            r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',
            r'Circular\s+No\.\s*\d+',
            r'A\.P\.\s*\(DIR\s*Series\)\s*Circular\s*No\.\s*\d+',
            r'DBOD\.No\.[A-Z]+\.BC\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            codes['all_matches'].extend(matches)
            
            if 'RBI/' in pattern:
                codes['rbi_codes'].extend(matches)
            elif 'Circular' in pattern:
                codes['circular_numbers'].extend(matches)
            else:
                codes['department_codes'].extend(matches)
        
        return codes
    
    def analyze_with_pdf_content(self, title: str, summary: str, pdf_link: str) -> dict:
        """Analyze notification with actual PDF content"""
        try:
            import openai
            client = openai.OpenAI(api_key=self.openai_api_key)
            
            # Extract PDF content
            pdf_text = self.download_and_extract_pdf_text(pdf_link)
            document_codes = self.extract_document_codes(pdf_text)
            
            # Enhanced prompt with PDF content
            prompt = f"""
You are an expert RBI regulatory analyst. Analyze this notification with its PDF content to extract specific document IDs and titles.

Notification:
Title: {title}
Summary: {summary}
PDF Link: {pdf_link}

PDF Content (first 2000 chars):
{pdf_text[:2000]}

Document Codes Found: {json.dumps(document_codes, indent=2)}

Extract SPECIFIC document IDs and titles from the content. For withdrawal notifications, identify the exact circular numbers being withdrawn. For updates, identify the specific documents being modified.

Respond in JSON format:
{{
    "category": "regulatory|informational|administrative|withdrawn",
    "specific_documents": [
        {{
            "document_id": "exact RBI code or circular number",
            "document_title": "specific document title from content",
            "action_needed": "ADD_DOCUMENT|REMOVE_DOCUMENT|UPDATE_DOCUMENT"
        }}
    ],
    "kb_actions": [
        {{
            "action_type": "ADD_DOCUMENT|REMOVE_DOCUMENT|UPDATE_DOCUMENT",
            "target_document": "specific document ID from content",
            "target_title": "specific document title",
            "collection": "rbi_circular|rbi_master_circular|rbi_master_direction|rbi_other",
            "priority": "HIGH|MEDIUM|LOW",
            "reasoning": "specific reason based on content",
            "details": "specific details from PDF content"
        }}
    ],
    "confidence": "high|medium|low",
    "reasoning": "analysis based on actual content"
}}
"""
            
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response.choices[0].message.content)
            result['pdf_codes_found'] = document_codes
            result['pdf_content_length'] = len(pdf_text)
            
            logger.info(f"✅ Enhanced analysis completed with {len(result.get('kb_actions', []))} specific actions")
            return result
            
        except Exception as e:
            logger.error(f"❌ Enhanced analysis failed: {e}")
            return {
                "category": "unknown",
                "specific_documents": [],
                "kb_actions": [],
                "confidence": "low",
                "reasoning": f"Analysis failed: {str(e)}",
                "pdf_codes_found": {},
                "pdf_content_length": 0
            }

def run_enhanced_test(category_name: str, max_tests: int = 2):
    """Run enhanced tests with PDF content analysis"""
    
    # Check for PyMuPDF
    try:
        import fitz
    except ImportError:
        logger.error("❌ PyMuPDF (fitz) not installed. Run: pip install PyMuPDF")
        return
    
    # Initialize enhanced pipeline
    logger.info("🚀 Initializing enhanced pipeline...")
    try:
        pipeline = EnhancedPipelineTest()
        logger.info("✅ Enhanced pipeline initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize pipeline: {e}")
        return
    
    # Load test file
    test_file = Path(f"pipeline_tests/{category_name}.json")
    if not test_file.exists():
        logger.error(f"❌ Test file not found: {test_file}")
        return
    
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    # Limit number of tests
    test_data = test_data[:max_tests]
    logger.info(f"📁 Running {len(test_data)} enhanced tests from {category_name}")
    
    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = Path(f"enhanced_test_outputs_{category_name}_{timestamp}")
    output_dir.mkdir(exist_ok=True)
    
    results = []
    
    for i, notification in enumerate(test_data):
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 Enhanced Test {i+1}/{len(test_data)}: {notification.get('title', 'Unknown')[:50]}...")
        
        try:
            # Extract notification details
            title = notification.get('title', '')
            summary = notification.get('summary', '')
            pdf_link = notification.get('pdf_link', '')
            expected_action = notification.get('action', '')
            
            # Enhanced analysis with PDF content
            logger.info("📊 Analyzing with PDF content...")
            analysis = pipeline.analyze_with_pdf_content(title, summary, pdf_link)
            
            # Compile result
            result = {
                'test_index': i + 1,
                'notification_id': notification.get('notification_id', ''),
                'title': title,
                'expected_action': expected_action,
                'pdf_link': pdf_link,
                'analysis': analysis,
                'status': 'success',
                'processing_timestamp': datetime.now().isoformat()
            }
            
            results.append(result)
            
            # Save individual result
            individual_file = output_dir / f"enhanced_test_{i+1}_{notification.get('notification_id', 'unknown')}.json"
            with open(individual_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ Enhanced test {i+1} completed successfully")
            
            # Print enhanced verification info
            print(f"\n📋 ENHANCED VERIFICATION - Test {i+1}:")
            print(f"Title: {title}")
            print(f"Expected Action: {expected_action}")
            print(f"LLM Category: {analysis.get('category', 'N/A')}")
            print(f"PDF Content Length: {analysis.get('pdf_content_length', 0)} chars")
            print(f"Document Codes Found: {len(analysis.get('pdf_codes_found', {}).get('all_matches', []))}")
            
            specific_docs = analysis.get('specific_documents', [])
            print(f"Specific Documents: {len(specific_docs)}")
            for j, doc in enumerate(specific_docs):
                print(f"  Doc {j+1}: {doc.get('document_id', 'N/A')} - {doc.get('document_title', 'N/A')[:50]}...")
            
            kb_actions = analysis.get('kb_actions', [])
            print(f"KB Actions: {len(kb_actions)}")
            for j, action in enumerate(kb_actions):
                print(f"  Action {j+1}: {action.get('action_type', 'N/A')} -> {action.get('target_document', 'N/A')}")
                print(f"    Title: {action.get('target_title', 'N/A')[:50]}...")
                print(f"    Collection: {action.get('collection', 'N/A')}")
            
        except Exception as e:
            logger.error(f"❌ Enhanced test {i+1} failed: {e}")
            logger.error(traceback.format_exc())
            
            result = {
                'test_index': i + 1,
                'notification_id': notification.get('notification_id', ''),
                'title': notification.get('title', ''),
                'expected_action': notification.get('action', ''),
                'error': str(e),
                'error_traceback': traceback.format_exc(),
                'status': 'error',
                'processing_timestamp': datetime.now().isoformat()
            }
            results.append(result)
    
    # Save summary
    summary = {
        'category': category_name,
        'total_tests': len(test_data),
        'successful_tests': len([r for r in results if r.get('status') == 'success']),
        'failed_tests': len([r for r in results if r.get('status') == 'error']),
        'total_kb_actions': sum(len(r.get('analysis', {}).get('kb_actions', [])) for r in results if r.get('status') == 'success'),
        'results': results,
        'processing_timestamp': datetime.now().isoformat()
    }
    
    summary_file = output_dir / "enhanced_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
    
    logger.info(f"\n🎉 Enhanced test run completed!")
    logger.info(f"📊 Results: {summary['successful_tests']}/{summary['total_tests']} successful")
    logger.info(f"📁 Output saved to: {output_dir}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python enhanced_test_runner.py <category_name> [max_tests]")
        print("Available categories:")
        test_dir = Path("pipeline_tests")
        for test_file in test_dir.glob("*.json"):
            print(f"  - {test_file.stem}")
        sys.exit(1)
    
    category_name = sys.argv[1]
    max_tests = int(sys.argv[2]) if len(sys.argv) > 2 else 2
    
    run_enhanced_test(category_name, max_tests)

if __name__ == "__main__":
    main()