#!/usr/bin/env python3
"""
Manual Review System for Low Vector Matches and Manual Database Addition
"""

import json
import logging
import os
import csv
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
# import pandas as pd  # Optional dependency

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class RemovalReviewItem:
    """Simplified structure for REMOVE operations requiring manual review"""
    item_id: str
    notification_title: str
    target_document: str
    reasoning: str
    timestamp: str
    # Essential fields for REMOVE operations
    point_ids: List[str] = None  # Qdrant point IDs that would be removed
    collection_name: str = ""
    confidence_score: float = 0.0
    # Review tracking
    review_status: str = "PENDING"
    reviewer_notes: str = ""
    final_decision: str = ""  # APPROVE_REMOVAL, REJECT_REMOVAL



class RemovalReviewSystem:
    """Simplified system for handling REMOVE operation reviews only"""

    def __init__(self, review_dir: str = "manual_reviews"):
        self.review_dir = review_dir

        # Create review directory
        os.makedirs(self.review_dir, exist_ok=True)

        # Initialize review files - simplified to just removal reviews
        self.removal_reviews_file = os.path.join(self.review_dir, "removal_reviews.json")
        self.completed_removals_file = os.path.join(self.review_dir, "completed_removals.json")

        logger.info(f"✅ Removal Review System initialized with directory: {self.review_dir}")

    def create_removal_review(self, notification_title: str, target_document: str,
                            reasoning: str, point_ids: List[str] = None,
                            collection_name: str = "", confidence_score: float = 0.0) -> str:
        """
        Create manual review item for REMOVE operations requiring verification

        Args:
            notification_title: Title of the notification requesting removal
            target_document: Target document identifier to be removed
            reasoning: Reason why manual review is needed
            point_ids: List of Qdrant point IDs that would be removed
            collection_name: Target collection name
            confidence_score: Confidence score of the match

        Returns:
            str: Review item ID
        """
        try:
            item_id = f"removal_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self._load_removal_reviews())}"

            # Create simple removal review item
            review_item = RemovalReviewItem(
                item_id=item_id,
                notification_title=notification_title,
                target_document=target_document,
                reasoning=reasoning,
                timestamp=datetime.now().isoformat(),
                point_ids=point_ids or [],
                collection_name=collection_name,
                confidence_score=confidence_score
            )

            # Save to removal reviews file
            reviews = self._load_removal_reviews()
            reviews.append(asdict(review_item))
            self._save_removal_reviews(reviews)

            logger.warning(f"🗑️ Created removal review: {item_id} for {target_document}")
            return item_id

        except Exception as e:
            logger.error(f"❌ Error creating removal review: {e}")
            return ""

    def get_pending_removals(self) -> List[Dict]:
        """Get all pending removal reviews"""
        reviews = self._load_removal_reviews()
        return [r for r in reviews if r.get('review_status') == 'PENDING']

    def _load_removal_reviews(self) -> List[Dict]:
        """Load removal reviews from file"""
        try:
            if os.path.exists(self.removal_reviews_file):
                with open(self.removal_reviews_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Error loading removal reviews: {e}")
            return []

    def _save_removal_reviews(self, reviews: List[Dict]):
        """Save removal reviews to file"""
        try:
            with open(self.removal_reviews_file, 'w') as f:
                json.dump(reviews, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving removal reviews: {e}")

    def get_review_summary(self) -> Dict[str, Any]:
        """Get summary statistics of manual reviews"""
        try:
            reviews = self._load_reviews()

            summary = {
                'total_reviews': len(reviews),
                'pending_reviews': len([r for r in reviews if r.get('review_status') == 'PENDING']),
                'completed_reviews': len([r for r in reviews if r.get('review_status') == 'COMPLETED']),
                'by_type': {},
                'by_priority': {},
                'by_action_type': {},
                'recent_reviews': []
            }

            # Count by type
            for review in reviews:
                item_type = review.get('item_type', 'unknown')
                summary['by_type'][item_type] = summary['by_type'].get(item_type, 0) + 1

                priority = review.get('priority', 'unknown')
                summary['by_priority'][priority] = summary['by_priority'].get(priority, 0) + 1

                action_type = review.get('action_type', 'unknown')
                if action_type:
                    summary['by_action_type'][action_type] = summary['by_action_type'].get(action_type, 0) + 1

            # Get recent reviews (last 10)
            recent_reviews = sorted(reviews, key=lambda x: x.get('timestamp', ''), reverse=True)[:10]
            summary['recent_reviews'] = [
                {
                    'item_id': r.get('item_id', ''),
                    'item_type': r.get('item_type', ''),
                    'action_type': r.get('action_type', ''),
                    'target_document': r.get('target_document', ''),
                    'priority': r.get('priority', ''),
                    'timestamp': r.get('timestamp', '')
                }
                for r in recent_reviews
            ]

            return summary

        except Exception as e:
            logger.error(f"❌ Error generating review summary: {e}")
            return {}

    def mark_review_completed(self, item_id: str, final_decision: str, reviewer_notes: str = "") -> bool:
        """Mark a review item as completed"""
        try:
            reviews = self._load_reviews()

            for review in reviews:
                if review.get('item_id') == item_id:
                    review['review_status'] = 'COMPLETED'
                    review['final_decision'] = final_decision
                    review['reviewer_notes'] = reviewer_notes
                    review['completion_timestamp'] = datetime.now().isoformat()
                    break
            else:
                logger.warning(f"Review item not found: {item_id}")
                return False

            self._save_reviews(reviews)
            logger.info(f"✅ Marked review {item_id} as completed with decision: {final_decision}")
            return True

        except Exception as e:
            logger.error(f"❌ Error marking review as completed: {e}")
            return False
    
    def evaluate_vector_match(self, vector_score: float, exact_match: bool, 
                            notification_data: Dict, target_document: str) -> bool:
        """
        Evaluate if a vector match requires manual review
        
        Args:
            vector_score: Similarity score from vector search (0.0 to 1.0)
            exact_match: Whether an exact ID/title match was found
            notification_data: Notification information
            target_document: Target document identifier
            
        Returns:
            bool: True if manual review is required
        """
        try:
            # Always require manual review if no exact match and low vector score
            if not exact_match and vector_score < self.vector_threshold_low:
                logger.warning(f"⚠️ Manual review required: No exact match, low vector score ({vector_score:.3f})")
                self._create_review_item(
                    item_type="low_vector_match",
                    notification_data=notification_data,
                    target_document=target_document,
                    vector_score=vector_score,
                    exact_match_found=exact_match,
                    reasoning=f"Low vector similarity score ({vector_score:.3f}) and no exact match found",
                    suggested_action="VERIFY_DOCUMENT_IDENTITY",
                    priority="HIGH"
                )
                return True
            
            # Require manual review for medium confidence without exact match
            if not exact_match and vector_score < self.vector_threshold_medium:
                logger.info(f"📋 Manual review recommended: Medium vector score ({vector_score:.3f}), no exact match")
                self._create_review_item(
                    item_type="no_exact_match",
                    notification_data=notification_data,
                    target_document=target_document,
                    vector_score=vector_score,
                    exact_match_found=exact_match,
                    reasoning=f"Medium vector similarity ({vector_score:.3f}) but no exact ID/title match",
                    suggested_action="CONFIRM_DOCUMENT_MATCH",
                    priority="MEDIUM"
                )
                return True
            
            # High confidence matches can proceed automatically
            if vector_score >= self.vector_threshold_high or exact_match:
                logger.info(f"✅ High confidence match: vector_score={vector_score:.3f}, exact_match={exact_match}")
                return False
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Error evaluating vector match: {e}")
            return True  # Default to manual review on error
    
    def _create_review_item(self, item_type: str, notification_data: Dict, 
                          target_document: str, vector_score: float, 
                          exact_match_found: bool, reasoning: str, 
                          suggested_action: str, priority: str) -> str:
        """Create a new manual review item"""
        try:
            item_id = f"review_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self._load_reviews())}"
            
            review_item = ManualReviewItem(
                item_id=item_id,
                item_type=item_type,
                notification_title=notification_data.get('title', 'Unknown'),
                notification_link=notification_data.get('link', ''),
                target_document=target_document,
                vector_score=vector_score,
                exact_match_found=exact_match_found,
                reasoning=reasoning,
                suggested_action=suggested_action,
                priority=priority,
                timestamp=datetime.now().isoformat()
            )
            
            # Save to review file
            reviews = self._load_reviews()
            reviews.append(asdict(review_item))
            self._save_reviews(reviews)
            
            logger.info(f"📝 Created manual review item: {item_id}")
            return item_id
            
        except Exception as e:
            logger.error(f"❌ Error creating review item: {e}")
            return ""
    
    def create_manual_addition_request(self, document_id: str, document_title: str,
                                     document_content: str, target_collection: str,
                                     document_type: str, metadata: Dict[str, Any],
                                     requester: str = "system") -> str:
        """
        Create a request for manual database addition
        
        Args:
            document_id: Unique document identifier
            document_title: Document title
            document_content: Full document content
            target_collection: Target Qdrant collection
            document_type: Type of document
            metadata: Additional metadata
            requester: Who requested the addition
            
        Returns:
            str: Request ID
        """
        try:
            request_id = f"add_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self._load_additions())}"
            
            addition_request = ManualAdditionRequest(
                request_id=request_id,
                document_id=document_id,
                document_title=document_title,
                document_content=document_content,
                target_collection=target_collection,
                document_type=document_type,
                metadata=metadata,
                requester=requester,
                timestamp=datetime.now().isoformat()
            )
            
            # Save to addition file
            additions = self._load_additions()
            additions.append(asdict(addition_request))
            self._save_additions(additions)
            
            logger.info(f"📝 Created manual addition request: {request_id}")
            return request_id
            
        except Exception as e:
            logger.error(f"❌ Error creating addition request: {e}")
            return ""
    
    def _load_reviews(self) -> List[Dict]:
        """Load existing review items"""
        try:
            if os.path.exists(self.review_file):
                with open(self.review_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"❌ Error loading reviews: {e}")
            return []
    
    def _save_reviews(self, reviews: List[Dict]):
        """Save review items to file"""
        try:
            with open(self.review_file, 'w') as f:
                json.dump(reviews, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Error saving reviews: {e}")
    
    def _load_additions(self) -> List[Dict]:
        """Load existing addition requests"""
        try:
            if os.path.exists(self.addition_file):
                with open(self.addition_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"❌ Error loading additions: {e}")
            return []
    
    def _save_additions(self, additions: List[Dict]):
        """Save addition requests to file"""
        try:
            with open(self.addition_file, 'w') as f:
                json.dump(additions, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Error saving additions: {e}")
    
    def generate_review_report(self) -> str:
        """Generate a comprehensive review report"""
        try:
            reviews = self._load_reviews()
            additions = self._load_additions()
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(self.review_dir, f"review_report_{timestamp}.md")
            
            with open(report_file, 'w') as f:
                f.write(f"# Manual Review Report\n")
                f.write(f"Generated: {datetime.now().isoformat()}\n\n")
                
                # Summary statistics
                f.write(f"## Summary\n")
                f.write(f"- Pending Reviews: {len([r for r in reviews if r['review_status'] == 'PENDING'])}\n")
                f.write(f"- Pending Additions: {len([a for a in additions if a['status'] == 'PENDING'])}\n")
                f.write(f"- Total Items: {len(reviews) + len(additions)}\n\n")
                
                # Pending reviews
                f.write(f"## Pending Reviews\n")
                pending_reviews = [r for r in reviews if r['review_status'] == 'PENDING']
                for review in pending_reviews:
                    f.write(f"### {review['item_id']}\n")
                    f.write(f"- **Type**: {review['item_type']}\n")
                    f.write(f"- **Priority**: {review['priority']}\n")
                    f.write(f"- **Notification**: {review['notification_title']}\n")
                    f.write(f"- **Target Document**: {review['target_document']}\n")
                    f.write(f"- **Vector Score**: {review['vector_score']:.3f}\n")
                    f.write(f"- **Exact Match**: {review['exact_match_found']}\n")
                    f.write(f"- **Reasoning**: {review['reasoning']}\n")
                    f.write(f"- **Suggested Action**: {review['suggested_action']}\n")
                    f.write(f"- **Link**: {review['notification_link']}\n\n")
                
                # Pending additions
                f.write(f"## Pending Manual Additions\n")
                pending_additions = [a for a in additions if a['status'] == 'PENDING']
                for addition in pending_additions:
                    f.write(f"### {addition['request_id']}\n")
                    f.write(f"- **Document ID**: {addition['document_id']}\n")
                    f.write(f"- **Title**: {addition['document_title']}\n")
                    f.write(f"- **Collection**: {addition['target_collection']}\n")
                    f.write(f"- **Type**: {addition['document_type']}\n")
                    f.write(f"- **Requester**: {addition['requester']}\n")
                    f.write(f"- **Content Length**: {len(addition['document_content'])} chars\n\n")
            
            logger.info(f"📊 Generated review report: {report_file}")
            return report_file
            
        except Exception as e:
            logger.error(f"❌ Error generating review report: {e}")
            return ""
    
    def export_to_csv(self) -> str:
        """Export review items to CSV for easy processing"""
        try:
            reviews = self._load_reviews()
            additions = self._load_additions()
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            csv_file = os.path.join(self.review_dir, f"manual_review_export_{timestamp}.csv")
            
            # Combine reviews and additions
            all_items = []
            
            for review in reviews:
                all_items.append({
                    'ID': review['item_id'],
                    'Type': 'REVIEW',
                    'Subtype': review['item_type'],
                    'Title': review['notification_title'],
                    'Target': review['target_document'],
                    'Vector_Score': review['vector_score'],
                    'Exact_Match': review['exact_match_found'],
                    'Priority': review['priority'],
                    'Status': review['review_status'],
                    'Timestamp': review['timestamp'],
                    'Link': review['notification_link']
                })
            
            for addition in additions:
                all_items.append({
                    'ID': addition['request_id'],
                    'Type': 'ADDITION',
                    'Subtype': addition['document_type'],
                    'Title': addition['document_title'],
                    'Target': addition['target_collection'],
                    'Vector_Score': 'N/A',
                    'Exact_Match': 'N/A',
                    'Priority': 'MEDIUM',
                    'Status': addition['status'],
                    'Timestamp': addition['timestamp'],
                    'Link': 'N/A'
                })
            
            # Save to CSV (without pandas dependency)
            if all_items:
                with open(csv_file, 'w', newline='') as f:
                    writer = csv.DictWriter(f, fieldnames=all_items[0].keys())
                    writer.writeheader()
                    writer.writerows(all_items)
            
            logger.info(f"📊 Exported to CSV: {csv_file}")
            return csv_file
            
        except Exception as e:
            logger.error(f"❌ Error exporting to CSV: {e}")
            return ""

if __name__ == "__main__":
    # Example usage
    review_system = ManualReviewSystem()
    
    # Example: Evaluate a vector match
    notification_data = {
        'title': 'Test Notification',
        'link': 'https://example.com'
    }
    
    needs_review = review_system.evaluate_vector_match(
        vector_score=0.55,
        exact_match=False,
        notification_data=notification_data,
        target_document='DBOD.No.BC.123/45.67.890/2024-25'
    )
    
    print(f"Needs manual review: {needs_review}")
    
    # Generate reports
    report_file = review_system.generate_review_report()
    csv_file = review_system.export_to_csv()
    
    print(f"Generated report: {report_file}")
    print(f"Generated CSV: {csv_file}")
