# Manual Review System Guide

## Overview
The Manual Review System automatically identifies cases where vector matches are low or exact ID/title matches aren't found, flagging them for human review and providing tools for manual database addition.

## When Manual Review is Triggered

### 1. Low Vector Similarity Scores
- **Low threshold**: < 0.6 (requires manual review)
- **Medium threshold**: < 0.75 (recommended for review if no exact match)
- **High threshold**: ≥ 0.85 (auto-approved)

### 2. Missing Exact Matches
- No exact document ID match found
- No exact title match found
- Combined with medium/low vector scores

### 3. Low Confidence Actions
- Actions marked with `confidence: LOW`
- Insufficient reasoning (< 20 characters for removals)
- Explicit `MANUAL_REVIEW_REQUIRED` markers

### 4. Removal Actions Without Strong Evidence
- Removal actions with weak evidence keywords
- Missing withdrawal/supersession indicators
- Unclear document references

## System Components

### 1. Manual Review System (`manual_review_system.py`)
**Purpose**: Core system for evaluating and tracking manual review items

**Key Features**:
- Vector match evaluation with configurable thresholds
- Review item creation and tracking
- Report generation (Markdown and CSV)
- Status management (PENDING, COMPLETED, FAILED)

**Usage**:
```python
from manual_review_system import ManualReviewSystem

review_system = ManualReviewSystem()

# Evaluate if manual review is needed
needs_review = review_system.evaluate_vector_match(
    vector_score=0.55,
    exact_match=False,
    notification_data={'title': 'Test Notification', 'link': 'https://...'},
    target_document='DBOD.No.BC.123/45.67.890/2024-25'
)
```

### 2. Manual Database Addition Tool (`manual_db_addition_tool.py`)
**Purpose**: Tool for manually adding documents to Qdrant collections

**Key Features**:
- Direct document addition to any collection
- Batch processing of pending addition requests
- Automatic chunking and embedding
- Collection validation

**CLI Usage**:
```bash
# List available collections
python manual_db_addition_tool.py --action list

# Process all pending addition requests
python manual_db_addition_tool.py --action process

# Add document directly
python manual_db_addition_tool.py --action add \
    --document-id "RBI/2024-25/123" \
    --title "Test Document" \
    --content-file "document.txt" \
    --collection "rbi_circular" \
    --document-type "manual"

# Add from specific request
python manual_db_addition_tool.py --action add --request-id "add_20250805_123456_1"
```

### 3. Pipeline Integration
**Purpose**: Automatic integration with the existing pipeline

**Features**:
- Automatic flagging during action validation
- Manual review file generation
- Vector match quality assessment
- Confidence-based filtering

## File Structure

### Review Files Location: `manual_reviews/`
- `pending_reviews.json` - Items awaiting manual review
- `manual_additions.json` - Requests for manual database addition
- `completed_reviews.json` - Completed review items
- `review_report_YYYYMMDD_HHMMSS.md` - Human-readable reports
- `manual_review_export_YYYYMMDD_HHMMSS.csv` - CSV exports for processing

## Workflow

### 1. Automatic Detection
```
Pipeline Processing → Vector Matching → Threshold Check → Manual Review Flag
```

### 2. Review Item Creation
```
Low Score/No Match → Create Review Item → Save to pending_reviews.json → Generate Report
```

### 3. Human Review Process
```
Review Report → Human Decision → Update Status → Process Action
```

### 4. Manual Addition
```
Addition Request → Validation → Add to Database → Update Status
```

## Review Item Types

### 1. `low_vector_match`
- **Trigger**: Vector score < 0.6 and no exact match
- **Priority**: HIGH
- **Action**: Verify document identity manually

### 2. `no_exact_match`
- **Trigger**: Vector score < 0.75 and no exact match
- **Priority**: MEDIUM
- **Action**: Confirm document match

### 3. `pipeline_action_review`
- **Trigger**: Pipeline-generated actions needing verification
- **Priority**: Varies
- **Action**: Review and approve/reject action

### 4. `failed_extraction`
- **Trigger**: Document extraction failures
- **Priority**: HIGH
- **Action**: Manual document processing

## Manual Review Process

### Step 1: Generate Review Report
```bash
python manual_review_system.py
```
This generates:
- `review_report_YYYYMMDD_HHMMSS.md` - Detailed report
- `manual_review_export_YYYYMMDD_HHMMSS.csv` - Spreadsheet format

### Step 2: Review Items
For each pending item:
1. **Check notification link** - Verify the source document
2. **Evaluate vector matches** - Review similarity scores
3. **Assess document identity** - Confirm target document is correct
4. **Make decision** - Approve, reject, or request more information

### Step 3: Process Decisions
```python
# Update review status
review_item['review_status'] = 'APPROVED'  # or 'REJECTED'
review_item['reviewer_notes'] = 'Verified document identity'
review_item['final_decision'] = 'PROCEED_WITH_ACTION'
```

### Step 4: Execute Approved Actions
```bash
# Process approved additions
python manual_db_addition_tool.py --action process
```

## Manual Database Addition

### Creating Addition Requests
```python
from manual_review_system import ManualReviewSystem

review_system = ManualReviewSystem()

request_id = review_system.create_manual_addition_request(
    document_id="RBI/2024-25/123",
    document_title="Manual Addition Test",
    document_content="Full document content here...",
    target_collection="rbi_circular",
    document_type="circular",
    metadata={"source": "manual", "priority": "high"},
    requester="admin"
)
```

### Processing Addition Requests
```bash
# Process all pending requests
python manual_db_addition_tool.py --action process

# Process specific request
python manual_db_addition_tool.py --action add --request-id "add_20250805_123456_1"
```

## Configuration

### Vector Thresholds (in `manual_review_system.py`)
```python
self.vector_threshold_low = 0.6     # Below this requires manual review
self.vector_threshold_medium = 0.75 # Medium confidence
self.vector_threshold_high = 0.85   # High confidence
```

### Collection Validation
Available collections:
- `rbi_circular` - Regular circulars and notifications
- `rbi_master_circular` - Master circulars
- `rbi_master_direction` - Master directions
- `rbi_other` - Other documents

## Monitoring and Reporting

### Daily Review Process
1. **Morning**: Generate review report
2. **Review**: Process pending items
3. **Afternoon**: Execute approved additions
4. **Evening**: Generate summary statistics

### Key Metrics to Track
- Number of items requiring manual review
- Vector score distributions
- Review completion rates
- Addition success rates
- Time to resolution

### Example Reports
```bash
# Generate comprehensive report
python -c "
from manual_review_system import ManualReviewSystem
rs = ManualReviewSystem()
print('Report:', rs.generate_review_report())
print('CSV:', rs.export_to_csv())
"
```

## Best Practices

### 1. Regular Review Cycles
- Check for pending reviews daily
- Process high-priority items first
- Maintain review logs

### 2. Quality Assurance
- Verify document sources before approval
- Cross-check vector matches manually
- Document decision reasoning

### 3. Batch Processing
- Group similar review items
- Process additions in batches
- Monitor system performance

### 4. Error Handling
- Always validate collection names
- Check document format before addition
- Maintain backup of review decisions

## Troubleshooting

### Common Issues
1. **High false positive rate**: Adjust vector thresholds
2. **Missing documents**: Check collection names and IDs
3. **Addition failures**: Verify content format and collection existence
4. **Performance issues**: Process in smaller batches

### Debug Commands
```bash
# Check system status
python manual_db_addition_tool.py --action list

# Validate specific collection
python -c "
from manual_db_addition_tool import ManualDatabaseAdder
adder = ManualDatabaseAdder()
print(adder.validate_collection('rbi_circular'))
"
```

## Integration with Existing Pipeline

The manual review system is automatically integrated into the pipeline:
- Actions are evaluated during validation
- Low-confidence items are flagged automatically
- Review files are generated with each pipeline run
- Manual additions can be processed independently

This ensures that the pipeline remains robust while providing human oversight for edge cases and uncertain matches.
