2025-08-04 15:14:35,578 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250804_151435.log
2025-08-04 15:14:35,578 - manual_test - INFO - 📝 Clean pipeline execution log configured
2025-08-04 15:14:38,425 - manual_test - INFO - 🚀 Starting Real Pipeline Test with Enhanced KB Storage...
2025-08-04 15:14:38,425 - manual_test - INFO - 📄 Using notifications file: rbi_notifications.json
2025-08-04 15:14:38,425 - manual_test - INFO - Initialized key rotator with 3 keys
2025-08-04 15:14:38,425 - manual_test - INFO - ✅ Environment setup complete
2025-08-04 15:14:38,426 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-04 15:14:38,427 - knowledge_base_executor - INFO - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250804_151438.log
2025-08-04 15:14:38,427 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 15:14:42,479 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 15:14:46,469 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-04 15:14:46,469 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 15:14:50,151 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-04 15:14:50,151 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...
2025-08-04 15:14:50,151 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with up to 50 notifications...
2025-08-04 15:14:50,152 - manual_test - INFO - 📂 Loaded 50 notifications from rbi_notifications.json
2025-08-04 15:14:50,152 - manual_test - INFO - 
2025-08-04 15:14:50,152 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-04 15:14:50,152 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 1/50 (2.0%)
2025-08-04 15:14:50,152 - manual_test - INFO - 🔄 Title: All Agency Banks To Remain Open For Public On March 31  2025...
2025-08-04 15:14:50,152 - manual_test - INFO - 🔄 Date: 
2025-08-04 15:14:50,152 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-04 15:14:50,152 - manual_test - INFO - 
2025-08-04 15:14:50,155 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-04 15:14:50,155 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-04 15:14:50,155 - manual_test - INFO - 🚀 ENHANCED Processing notification: All Agency Banks To Remain Open For Public On Marc...
2025-08-04 15:14:50,155 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-04 15:14:50,155 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:50,197 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:50,220 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:50,222 - manual_test - INFO -    📦 Chunked HTML AST into 1 chunks
2025-08-04 15:14:50,222 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2024-25/112, Long: 
2025-08-04 15:14:50,222 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2024-25/112', 'long_code': '', 'full_code': 'RBI/2024-25/112', 'year': '2024', 'all_codes': ['RBI/2024-25/112']}
2025-08-04 15:14:50,222 - manual_test - INFO -    ✅ Extracted 1 chunks from local PDF
2025-08-04 15:14:50,222 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-04 15:14:50,223 - manual_test - INFO -    📋 Short code: RBI/2024-25/112
2025-08-04 15:14:50,223 - manual_test - INFO -    📋 Long code: 
2025-08-04 15:14:50,223 - manual_test - INFO -    📅 Year from codes: 2024
2025-08-04 15:14:50,223 - manual_test - INFO - ✅ Extracted 3229 chars from local PDF
2025-08-04 15:14:50,223 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:50,223 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:50,238 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:50,259 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:50,259 - manual_test - INFO -    📦 Chunked HTML AST into 1 chunks
2025-08-04 15:14:50,260 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2024-25/112, Long: 
2025-08-04 15:14:50,260 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2024-25/112', 'long_code': '', 'full_code': 'RBI/2024-25/112', 'year': '2024', 'all_codes': ['RBI/2024-25/112']}
2025-08-04 15:14:50,260 - manual_test - INFO -    ✅ Extracted 1 chunks from local PDF
2025-08-04 15:14:50,260 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-04 15:14:50,260 - manual_test - INFO -    📋 Short code: RBI/2024-25/112
2025-08-04 15:14:50,260 - manual_test - INFO -    📋 Long code: 
2025-08-04 15:14:50,260 - manual_test - INFO -    📅 Year from codes: 2024
2025-08-04 15:14:50,260 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2024-25/112', 'long_code': '', 'full_code': 'RBI/2024-25/112', 'year': '2024', 'all_codes': ['RBI/2024-25/112'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-04 15:14:50,260 - manual_test - INFO -    📋 Short code: RBI/2024-25/112
2025-08-04 15:14:50,260 - manual_test - INFO -    📋 Long code: 
2025-08-04 15:14:50,260 - manual_test - INFO -    📋 Full code: RBI/2024-25/112
2025-08-04 15:14:50,260 - manual_test - INFO -    📅 Year: 2024
2025-08-04 15:14:50,260 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-04 15:14:50,260 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-04 15:14:50,260 - manual_test - INFO - 🔍 Starting notification analysis for: All Agency Banks To Remain Open For Public On Marc...
2025-08-04 15:14:50,261 - manual_test - INFO - 📏 Notification length: 3229 characters
2025-08-04 15:14:50,261 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-04 15:14:50,264 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 15:14:53,037 - manual_test - INFO - ✅ Notification categorized as: Informational (confidence: high)
2025-08-04 15:14:53,040 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected complex tier for regulatory_interpretation (context: 679, priority: quality)
2025-08-04 15:14:56,218 - manual_test - INFO - 📋 KB Decision: store_new (store: True, remove: False)
2025-08-04 15:14:56,219 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-04 15:14:56,219 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/2024-25/112', 'long_code': '', 'full_code': 'RBI/2024-25/112', 'year': '2024', 'all_codes': ['RBI/2024-25/112'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-04 15:14:56,219 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-04 15:14:56,219 - manual_test - INFO - 🔍 Detecting document type from title: All Agency Banks To Remain Open For Public On March 31  2025  Monday ...
2025-08-04 15:14:56,219 - manual_test - INFO - 📋 Target collection from KB decision: rbi_other
2025-08-04 15:14:56,219 - manual_test - INFO - ✅ Detected: Other
2025-08-04 15:14:56,219 - manual_test - INFO - 📋 Document Type Detected: other
2025-08-04 15:14:56,219 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: other
2025-08-04 15:14:56,219 - manual_test - INFO - 📑 Processing Other document actions...
Type of local_file_path: <class 'str'>
Converted local_file_path to Path: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
Final local_file_path type: <class 'pathlib.PosixPath'>, value: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
Creating S3 client with access key: AKIARWPFIS35YOMG2O5Q, region: ap-south-1
Using S3 client: <botocore.client.S3 object at 0x339d250d0>
Uploading file to S3: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf, bucket: localdocdump, key: notifications/rbi_other/RBI_2024-25_112.pdf
Extra args for upload: {'ContentType': 'application/pdf', 'ContentDisposition': 'inline', 'Metadata': {'document_id': 'RBI/2024-25/112', 'collection': 'rbi_other', 'document_type': 'notification'}}
Upload successful! S3 URL: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_other/RBI_2024-25_112.pdf
2025-08-04 15:14:56,833 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_other/RBI_2024-25_112.pdf
2025-08-04 15:14:56,833 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Other document
2025-08-04 15:14:56,833 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-04 15:14:56,833 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_other
2025-08-04 15:14:56,833 - manual_test - INFO -       🎯 Target: RBI/2024-25/112
2025-08-04 15:14:56,833 - manual_test - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 15:14:56,833 - manual_test - INFO -       📊 Confidence: 0.9
Type of local_file_path: <class 'str'>
Converted local_file_path to Path: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
Final local_file_path type: <class 'pathlib.PosixPath'>, value: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
Creating S3 client with access key: AKIARWPFIS35YOMG2O5Q, region: ap-south-1
Using S3 client: <botocore.client.S3 object at 0x339df28b0>
Uploading file to S3: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf, bucket: localdocdump, key: notifications/rbi_other/RBI_2024-25_112.pdf
Extra args for upload: {'ContentType': 'application/pdf', 'ContentDisposition': 'inline', 'Metadata': {'document_id': 'RBI/2024-25/112', 'collection': 'rbi_other', 'document_type': 'notification'}}
Upload successful! S3 URL: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_other/RBI_2024-25_112.pdf
2025-08-04 15:14:57,036 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_other/RBI_2024-25_112.pdf
2025-08-04 15:14:57,037 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-04 15:14:57,037 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_other
2025-08-04 15:14:57,037 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 15:14:57,037 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:57,037 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:14:57,037 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 15:14:57,037 - knowledge_base_executor - INFO - [QDRANT] ✅ Using document_id: RBI/2024-25/112
2025-08-04 15:14:57,037 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=9e84af1e-732e-5b48-86c6-92c6dd140d13, Document=RBI/2024-25/112, Chunk=None
2025-08-04 15:14:57,037 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 6475 chars
2025-08-04 15:15:00,457 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: 9e84af1e-732e-5b48-86c6-92c6dd140d13)
2025-08-04 15:15:00,457 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: 9e84af1e-732e-5b48-86c6-92c6dd140d13) to collections: ['rbi_other']", "timestamp": "2025-08-04T09:45:00.457570", "document_id": "RBI/2024-25/112", "uuid": "9e84af1e-732e-5b48-86c6-92c6dd140d13", "collection": "rbi_other"}
2025-08-04 15:15:00,457 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 15:15:00,457 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 15:15:00,459 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 15:15:00,459 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-04 15:15:00,459 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-04 15:15:00,459 - manual_test - INFO -       📦 Collection: rbi_other
2025-08-04 15:15:00,459 - manual_test - INFO -       🆔 UUID: 9e84af1e-732e-5b48-86c6-92c6dd140d13
2025-08-04 15:15:00,459 - manual_test - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 15:15:00,459 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-04 15:15:00,459 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['9e84af1e-732e-5b48-86c6-92c6dd140d13']
2025-08-04 15:15:00,459 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-04 15:15:02,763 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 15:15:06,695 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: b4fd5a8d-2d86-473b-84ae-d0b16bcd9b9a
2025-08-04 15:15:06,695 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-04 15:15:06,695 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-04 15:15:06,726 - manual_test - INFO - ✅ Added notification to metadata_vectors: b4fd5a8d-2d86-473b-84ae-d0b16bcd9b9a
2025-08-04 15:15:06,726 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-04 15:15:06,726 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-04 15:15:06,726 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:15:06,741 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:15:06,761 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:15:06,762 - manual_test - INFO -    📦 Chunked HTML AST into 1 chunks
2025-08-04 15:15:06,763 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2024-25/112, Long: 
2025-08-04 15:15:06,763 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2024-25/112', 'long_code': '', 'full_code': 'RBI/2024-25/112', 'year': '2024', 'all_codes': ['RBI/2024-25/112']}
2025-08-04 15:15:06,763 - manual_test - INFO -    ✅ Extracted 1 chunks from local PDF
2025-08-04 15:15:06,763 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-04 15:15:06,763 - manual_test - INFO -    📋 Short code: RBI/2024-25/112
2025-08-04 15:15:06,763 - manual_test - INFO -    📋 Long code: 
2025-08-04 15:15:06,763 - manual_test - INFO -    📅 Year from codes: 2024
2025-08-04 15:15:06,763 - manual_test - INFO - 🔧 Starting streamlined processing of 1 chunks
2025-08-04 15:15:06,763 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 1/1
2025-08-04 15:15:06,763 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 3229 chars
2025-08-04 15:15:06,763 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-04 15:15:06,763 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "All Agency Banks To Remain Open For Public On March 31  2025  Monday ", "notification_date": ""}
2025-08-04 15:15:06,763 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 3229 chars
2025-08-04 15:15:06,764 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-04 15:15:06,764 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-04 15:15:06,764 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o-mini for document_classification - Selected simple tier for document_classification (context: 543, priority: balanced)
2025-08-04 15:15:09,336 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-04 15:15:09,336 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_other",
  "reasoning": "The document provides a directive from the RBI regarding the ope...
2025-08-04 15:15:09,336 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-04 15:15:09,337 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-04 15:15:09,337 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_other", "reasoning": "The document provides a directive from the RBI regarding the operational status of agency banks on a specific date, which is a regulatory requirement for compliance with government transactions. It is relevant for banks to understand their obligations, thus it should be indexed for easy access and reference."}
2025-08-04 15:15:09,337 - manual_test - INFO - ✅ Chunk 1 classified as regulatory - INDEXING
2025-08-04 15:15:11,872 - manual_test - INFO - 🎯 Executing 1 actions for chunk 1
2025-08-04 15:15:11,872 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-04 15:15:11,873 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-04 15:15:11,873 - manual_test - INFO - 📝 Sample action: {'document_id': 'RBI/2024-25/112', 'action_type': 'ADD_DOCUMENT', 'confidence': 'high', 'document_title': 'All Agency Banks to Remain Open for Public on March 31, 2025', 'document_url': None, 'reference_number': 'DOR.CO.SOG(Leg) No.59/09.08.024/2024-25', 'department': 'DOR', 'original_date': '2025-02-11', 'update_location': None, 'sunset_withdraw_date': None, 'reasoning': 'New directive for agency banks to remain open on a public holiday for government transactions.'}
2025-08-04 15:15:11,873 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-04 15:15:11,874 - manual_test - INFO - 📌 Generated action ID for Pydantic model: ADD_DOCUMENT_RBI_2024-25_112_chunk0_4a93edbc
2025-08-04 15:15:11,874 - manual_test - INFO - 🔍 Validating Pydantic action: ADD_DOCUMENT for RBI/2024-25/112
2025-08-04 15:15:11,874 - manual_test - INFO - 🎯 Executing ADD_DOCUMENT action for Pydantic model with document_id: RBI/2024-25/112
2025-08-04 15:15:11,874 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 15:15:11,874 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:15:11,875 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:15:11,875 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 15:15:11,876 - knowledge_base_executor - INFO - [QDRANT] ✅ Using document_id: RBI/2024-25/112
2025-08-04 15:15:11,876 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=78fc2586-566e-5a71-9674-a905a0f4d877, Document=RBI/2024-25/112, Chunk=0
2025-08-04 15:15:11,877 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 3340 chars
2025-08-04 15:15:14,690 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: 78fc2586-566e-5a71-9674-a905a0f4d877)
2025-08-04 15:15:14,690 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: 78fc2586-566e-5a71-9674-a905a0f4d877) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T09:45:14.690744", "document_id": "RBI/2024-25/112", "uuid": "78fc2586-566e-5a71-9674-a905a0f4d877", "collection": "rbi_master_direction"}
2025-08-04 15:15:14,690 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 15:15:14,690 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 15:15:14,691 - manual_test - INFO - ✅ Action executed successfully: ADD_DOCUMENT - RBI/2024-25/112
2025-08-04 15:15:14,691 - manual_test - INFO - 📊 Chunk action execution: 1/1 successful
2025-08-04 15:15:14,691 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 1:
2025-08-04 15:15:14,691 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2024-25/112
2025-08-04 15:15:14,691 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2024-25/112
2025-08-04 15:15:14,691 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - [QDRANT] ✅ Using document_id: RBI/2024-25/112
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=8b69a515-b921-54d4-a041-1fbadaee5466, Document=RBI/2024-25/112, Chunk=0
2025-08-04 15:15:14,691 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 3229 chars
2025-08-04 15:15:17,987 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: 8b69a515-b921-54d4-a041-1fbadaee5466)
2025-08-04 15:15:17,987 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: 8b69a515-b921-54d4-a041-1fbadaee5466) to collections: ['rbi_circular']", "timestamp": "2025-08-04T09:45:17.987587", "document_id": "RBI/2024-25/112", "uuid": "8b69a515-b921-54d4-a041-1fbadaee5466", "collection": "rbi_circular"}
2025-08-04 15:15:17,987 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 15:15:17,987 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 15:15:17,988 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 15:15:17,988 - manual_test - INFO - 🆔 CHUNK UUID: 8b69a515-b921-54d4-a041-1fbadaee5466
2025-08-04 15:15:17,988 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 1
2025-08-04 15:15:17,988 - manual_test - INFO -    ✅ Chunk 1 added to processing queue
2025-08-04 15:15:17,988 - manual_test - INFO - � Chunk Processing Summary:
2025-08-04 15:15:17,988 - manual_test - INFO -    - Total chunks: 1
2025-08-04 15:15:17,988 - manual_test - INFO -    - Indexed chunks: 1
2025-08-04 15:15:17,988 - manual_test - INFO -    - Skipped chunks: 0
2025-08-04 15:15:17,988 - manual_test - INFO -    - Chunks producing actions: 1
2025-08-04 15:15:17,988 - manual_test - INFO - ✅ STREAMLINED PDF processing: 1 chunks, 1 actions executed
2025-08-04 15:15:17,988 - manual_test - INFO - ✅ Notification 1/50 processed successfully - 0 documents added
2025-08-04 15:15:17,988 - manual_test - INFO - 
2025-08-04 15:15:17,988 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-04 15:15:17,988 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 2/50 (4.0%)
2025-08-04 15:15:17,988 - manual_test - INFO - 🔄 Title: Alteration In The Name Of  North East Small Finance Bank Lim...
2025-08-04 15:15:17,988 - manual_test - INFO - 🔄 Date: 
2025-08-04 15:15:17,988 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-04 15:15:17,988 - manual_test - INFO - 
2025-08-04 15:15:17,988 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-04 15:15:17,988 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-04 15:15:17,988 - manual_test - INFO - 🚀 ENHANCED Processing notification: Alteration In The Name Of  North East Small Financ...
2025-08-04 15:15:17,988 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-04 15:15:17,989 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 15:15:18,003 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 15:15:18,024 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 15:15:18,025 - manual_test - INFO -    📦 Chunked HTML AST into 1 chunks
2025-08-04 15:15:18,026 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/38, Long: DoR.RET.REC.20/12.07.160/2025-26
2025-08-04 15:15:18,026 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/38', 'long_code': 'DoR.RET.REC.20/12.07.160/2025-26', 'full_code': 'RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26', 'year': '2025', 'all_codes': ['DoR.RET.REC.20/12.07.160/2025-26', '.REC.20/12.07.160/2025-26', 'DoR.RET.REC.20/12.07.160', 'DoR.RET.REC.20/12.07.160/2025', 'RBI/2025-26/38']}
2025-08-04 15:15:18,026 - manual_test - INFO -    ✅ Extracted 1 chunks from local PDF
2025-08-04 15:15:18,026 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-04 15:15:18,026 - manual_test - INFO -    📋 Short code: RBI/2025-26/38
2025-08-04 15:15:18,026 - manual_test - INFO -    📋 Long code: DoR.RET.REC.20/12.07.160/2025-26
2025-08-04 15:15:18,026 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-04 15:15:18,026 - manual_test - INFO - ✅ Extracted 5161 chars from local PDF
2025-08-04 15:15:18,027 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 15:15:18,027 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 15:15:18,042 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 15:15:18,063 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 15:15:18,064 - manual_test - INFO -    📦 Chunked HTML AST into 1 chunks
2025-08-04 15:15:18,065 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/38, Long: DoR.RET.REC.20/12.07.160/2025-26
2025-08-04 15:15:18,065 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/38', 'long_code': 'DoR.RET.REC.20/12.07.160/2025-26', 'full_code': 'RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26', 'year': '2025', 'all_codes': ['DoR.RET.REC.20/12.07.160/2025-26', '.REC.20/12.07.160/2025-26', 'DoR.RET.REC.20/12.07.160', 'DoR.RET.REC.20/12.07.160/2025', 'RBI/2025-26/38']}
2025-08-04 15:15:18,065 - manual_test - INFO -    ✅ Extracted 1 chunks from local PDF
2025-08-04 15:15:18,065 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-04 15:15:18,065 - manual_test - INFO -    📋 Short code: RBI/2025-26/38
2025-08-04 15:15:18,065 - manual_test - INFO -    📋 Long code: DoR.RET.REC.20/12.07.160/2025-26
2025-08-04 15:15:18,065 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-04 15:15:18,065 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2025-26/38', 'long_code': 'DoR.RET.REC.20/12.07.160/2025-26', 'full_code': 'RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26', 'year': '2025', 'all_codes': ['DoR.RET.REC.20/12.07.160/2025-26', '.REC.20/12.07.160/2025-26', 'DoR.RET.REC.20/12.07.160', 'DoR.RET.REC.20/12.07.160/2025', 'RBI/2025-26/38'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-04 15:15:18,065 - manual_test - INFO -    📋 Short code: RBI/2025-26/38
2025-08-04 15:15:18,065 - manual_test - INFO -    📋 Long code: DoR.RET.REC.20/12.07.160/2025-26
2025-08-04 15:15:18,065 - manual_test - INFO -    📋 Full code: RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26
2025-08-04 15:15:18,065 - manual_test - INFO -    📅 Year: 2025
2025-08-04 15:15:18,065 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-04 15:15:18,065 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-04 15:15:18,065 - manual_test - INFO - 🔍 Starting notification analysis for: Alteration In The Name Of  North East Small Financ...
2025-08-04 15:15:18,065 - manual_test - INFO - 📏 Notification length: 5161 characters
2025-08-04 15:15:18,065 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-04 15:15:18,068 - manual_test - INFO - Making LLM call with model: gpt-4.1
