def llm_get_list_actions_from_chunk(self, chunk_content: str, metadata: dict) -> List[DocumentAction]:
    """
    Use LLM to extract a list of actions from a document chunk.
    
    Args:
        chunk_content: The text content of the chunk
        metadata: Additional metadata for the chunk
        
    Returns:
        List[DocumentAction]: List of actions extracted from the chunk
    """
    import logging
    import traceback
    import json
    from typing import List
    from prompts.notification_categorizer import DocumentAction
    from pydantic import BaseModel, Field
    from typing import List as PydanticList

    # Define a container class for structured output
    class DocumentActionsList(BaseModel):
        actions: PydanticList[DocumentAction] = Field(default_factory=list)

    try:
        # Convert metadata to a string representation for the prompt
        if isinstance(metadata, dict):
            metadata_str = json.dumps(metadata, default=str, indent=2)
        else:
            metadata_str = str(metadata)

        # Create a system prompt for document action extraction
        system_prompt = """
You are an expert RBI regulatory document analyzer. Your task is to analyze notification chunks and extract document actions.
Identify any affected documents mentioned in the content and determine what action is required for each document.

For each document, you must extract:
1. document_id: Primary document identifier (reference number or title) - REQUIRED
2. action_type: One of [UPDATE_DOCUMENT, REMOVE_DOCUMENT, ADD_DOCUMENT, ADD_TEMPORARY_NOTE, NO_ACTION] - REQUIRED
3. confidence: Confidence level (high, medium, low) - REQUIRED
4. Optional fields if available: document_title, document_url, reference_number, department, original_date, update_location, sunset_withdraw_date, reasoning
"""

        user_prompt = f"""
Document Metadata:
{metadata_str}

Document Chunk:
{chunk_content[:5000]}  # Limit chunk size to avoid token issues

Extract all affected documents and their required actions from this chunk.
Format your response as a JSON object with an 'actions' array containing document action objects.
Example:
{{
  "actions": [
    {{
      "document_id": "RBI/2024-25/123",
      "action_type": "UPDATE_DOCUMENT",
      "confidence": "high",
      "document_title": "Master Direction on Digital Lending",
      "update_location": "paragraph 5.2"
    }},
    {{
      "document_id": "Master Circular on Import of Goods",
      "action_type": "REMOVE_DOCUMENT", 
      "confidence": "medium",
      "sunset_withdraw_date": "2025-08-01"
    }}
  ]
}}

If no actions are found, return {{ "actions": [] }}
"""
        
        logger.info(f"Sending structured request to extract document actions")
        
        # Get completion with structured output
        response = self.openai_manager.get_completion(
            prompt=f"{system_prompt}\n\n{user_prompt}",
            model='gpt-4',
            temperature=0.1,
            response_format={"type": "json_object"}
        )
        
        document_actions = []
        
        try:
            # Handle different response formats
            if isinstance(response, str):
                # Parse JSON string response
                try:
                    parsed_response = json.loads(response)
                    actions_data = parsed_response.get('actions', [])
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error: {e}")
                    logger.error(f"Raw response: {response[:500]}...")
                    return []
            elif isinstance(response, dict):
                # Use dictionary response directly
                actions_data = response.get('actions', [])
            else:
                logger.error(f"Unexpected response type: {type(response)}")
                return []
            
            if not actions_data:
                logger.info("No document actions found in content")
                return []
            
            # Process each action and create DocumentAction objects
            for i, action_data in enumerate(actions_data):
                try:
                    # Ensure all required fields are present
                    required_fields = ['document_id', 'action_type', 'confidence']
                    for field in required_fields:
                        if field not in action_data:
                            if field == 'document_id':
                                action_data['document_id'] = f"unknown_doc_{i+1}"
                            elif field == 'action_type':
                                action_data['action_type'] = "NO_ACTION"
                            elif field == 'confidence':
                                action_data['confidence'] = "low"
                    
                    # Create DocumentAction object with proper error handling
                    try:
                        action = DocumentAction(**action_data)
                        document_actions.append(action)
                        logger.info(f"Created DocumentAction: {action.document_id}, {action.action_type}")
                    except Exception as validation_error:
                        logger.error(f"Validation error for DocumentAction: {str(validation_error)}")
                        logger.error(f"Problematic data: {json.dumps(action_data, default=str)}")
                except Exception as e:
                    logger.error(f"Error processing action data: {str(e)}")
            
            return document_actions
        
        except Exception as parsing_error:
            logger.error(f"Error parsing LLM response: {str(parsing_error)}")
            logger.error(traceback.format_exc())
            return []
                    
    except Exception as e:
        logger.error(f"Error in llm_get_list_actions_from_chunk: {str(e)}")
        logger.error(traceback.format_exc())
        return []
