from sentence_transformers import Sen<PERSON>ceTransformer
from qdrant_client import Qdrant<PERSON>lient, models

# CONFIGURATION
QDRANT_URL = "http://localhost:6333"
ORIGINAL_COLLECTION = "rbi_other"  # <- Set your original collection name here
NEW_COLLECTION = "metadata_vectors"
EMBED_FIELDS = ["document_id", "document_title", "document_number", "short_summary"]
BATCH_SIZE = 100

# Initialize Model & Client
model = SentenceTransformer("all-MiniLM-L6-v2")
client = QdrantClient(QDRANT_URL)

# Create New Collection with Named Vectors
def create_collection(client, collection_name, fields, dimension):
    if client.collection_exists(collection_name):
        client.delete_collection(collection_name)
    vectors_config = {
        f"{field}_vec": models.VectorParams(size=dimension, distance=models.Distance.COSINE)
        for field in fields
    }
    client.create_collection(
        collection_name=collection_name,
        vectors_config=vectors_config
    )
    print(f"Created collection '{collection_name}'.")

# Fetch existing points from your Qdrant collection
def fetch_existing_docs(client, collection_name, limit=1000):
    all_points = []
    next_page = None

    while True:
        records, next_page = client.scroll(
            collection_name=collection_name,
            limit=BATCH_SIZE,
            offset=next_page,
            with_payload=True,
            with_vectors=False
        )

        all_points.extend(records)

        if next_page is None or len(records) == 0 or len(all_points) >= limit:
            break

    print(f"Fetched {len(all_points)} documents from '{collection_name}'.")
    return all_points

# Insert metadata points aligned with existing Qdrant point IDs
def insert_metadata_vectors(client, collection_name, docs, fields, model):
    points = []
    for doc in docs:
        vectors = {
            f"{field}_vec": model.encode(doc.payload.get(field, "")).tolist()
            for field in fields
        }
        points.append(models.PointStruct(
            id=doc.id,  # original point ID alignment
            vector=vectors,
            payload={
                "document_id": doc.payload.get("document_id"),
                "collection_name": ORIGINAL_COLLECTION
            }
        ))

        if len(points) >= BATCH_SIZE:
            client.upload_points(collection_name, points)
            points.clear()

    # Insert remaining points
    if points:
        client.upload_points(collection_name, points)

    print(f"Inserted metadata vectors into '{collection_name}'.")

# Main execution
def main():
    vector_dim = model.get_sentence_embedding_dimension()

    # check if collection exists, create if not
    if not client.collection_exists(NEW_COLLECTION):
        create_collection(client, NEW_COLLECTION, EMBED_FIELDS, vector_dim)

    # Fetch docs from existing collection
    docs = fetch_existing_docs(client, ORIGINAL_COLLECTION, limit=30000)  # Adjust limit as needed

    # Insert metadata vectors aligned with original point IDs
    insert_metadata_vectors(client, NEW_COLLECTION, docs, EMBED_FIELDS, model)

if __name__ == "__main__":
    main()