2025-08-04 08:23:05,277 - INFO - 🚀 Starting removal action tests...
2025-08-04 08:23:05,277 - INFO - ============================================================
2025-08-04 08:23:05,277 - INFO - 
📋 Running test: Re<PERSON><PERSON><PERSON>
2025-08-04 08:23:05,277 - INFO - 🧪 Testing improved removal handler...
2025-08-04 08:23:05,280 - INFO - ✅ PASS Valid RBI document ID: 'RBI/FED/2024-25/17' -> valid=True, reason=''
2025-08-04 08:23:05,280 - WARNING - ⚠️ MANUAL_REVIEW_REQUIRED document ID detected: MANUAL_REVIEW_REQUIRED
2025-08-04 08:23:05,280 - INFO - ✅ PASS Should now allow with warning: 'MANUAL_REVIEW_REQUIRED' -> valid=True, reason='MANUAL_REVIEW_REQUIRED - will attempt vector search fallback'
2025-08-04 08:23:05,280 - INFO - ✅ PASS Valid DBR document ID: 'DBR.No.Ret.BC.78/12.02.001/2024-25' -> valid=True, reason=''
2025-08-04 08:23:05,280 - INFO - ✅ PASS Should reject preserved documents: 'PRESERVE_DOCUMENT' -> valid=False, reason='Documents marked for preservation cannot be removed'
2025-08-04 08:23:05,280 - INFO - ✅ PASS Should reject test documents: 'TEST_123' -> valid=False, reason='Test documents should be removed using test tools'
2025-08-04 08:23:05,280 - INFO - ✅ PASS Should reject empty document ID: '' -> valid=False, reason='Empty document ID'
2025-08-04 08:23:05,280 - INFO - 
🔧 Testing document ID normalization...
2025-08-04 08:23:05,280 - INFO - ✅ PASS 'RBI/FED/2024-25/17/' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:23:05,280 - INFO - ✅ PASS 'RBI\FED\2024-25\17' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:23:05,280 - INFO - ✅ PASS 'RBI/FED/2024-25/17 ' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:23:05,280 - INFO - ✅ PASSED Removal Handler
2025-08-04 08:23:05,280 - INFO - 
📋 Running test: KB Executor Removal
2025-08-04 08:23:05,280 - INFO - 
🧪 Testing KB executor removal logic...
2025-08-04 08:23:06,036 - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-04 08:23:08,585 - INFO - Configuration loaded successfully
2025-08-04 08:23:08,918 - INFO - Configuration loaded successfully
2025-08-04 08:23:10,442 - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-04 08:23:10,443 - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-04 08:23:10,458 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:23:10,462 - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-04 08:23:10,463 - INFO - ✅ QdrantClient initialized successfully
2025-08-04 08:23:11,316 - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-04 08:23:11,317 - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-04 08:23:11,317 - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-04 08:23:11,328 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082311.log
2025-08-04 08:23:11,328 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:23:11,406 - INFO - Use pytorch device_name: mps
2025-08-04 08:23:11,406 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:23:15,110 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:23:15,110 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:23:15,110 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:23:15,110 - INFO - 🎯 Testing removal action with MANUAL_REVIEW_REQUIRED...
2025-08-04 08:23:15,110 - INFO - Action: {'action_type': 'REMOVE_DOCUMENT', 'target_document': 'MANUAL_REVIEW_REQUIRED', 'collection_name': ['rbi_circular'], 'use_vector_search': True, 'document_number': 'Test Document Number'}
2025-08-04 08:23:15,111 - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action_type": "REMOVE_DOCUMENT", "target_document": "MANUAL_REVIEW_REQUIRED", "collection_name": ["rbi_circular"], "use_vector_search": true, "document_number": "Test Document Number"}
2025-08-04 08:23:15,111 - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_circular']
2025-08-04 08:23:15,112 - WARNING - ⚠️ MANUAL_REVIEW_REQUIRED document ID detected: MANUAL_REVIEW_REQUIRED
2025-08-04 08:23:15,112 - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-04 08:23:15,112 - INFO - [QDRANT] 🔍 Search parameters: document_id='MANUAL_REVIEW_REQUIRED', document_number='Test Document Number'
2025-08-04 08:23:15,114 - INFO - Use pytorch device_name: mps
2025-08-04 08:23:15,114 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:23:18,380 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:23:18,380 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:23:18,380 - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_circular']
2025-08-04 08:23:18,381 - INFO - [QDRANT] 🔍 SKIPPING DOCUMENT ID SEARCH (MANUAL_REVIEW_REQUIRED)
2025-08-04 08:23:18,381 - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT NUMBER: 'Test Document Number'
2025-08-04 08:23:18,485 - INFO - Searching metadata_vectors for 'Test Document Number' in field 'document_number'
2025-08-04 08:23:18,507 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:23:18,524 - INFO - Found 10 results for metadata search
2025-08-04 08:23:18,524 - INFO - [QDRANT] ✅ Found 10 matches by document number
2025-08-04 08:23:18,524 - INFO - [QDRANT] 📋 DOCUMENT NUMBER SEARCH RESULTS:
2025-08-04 08:23:18,524 - INFO - [QDRANT]    1. ID=8a26f3b3-4845-4e05-bca1-cebfaf86353a | Score=0.3617 | DocID=RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    2. ID=0bceb959-f4eb-4ac7-879c-4a70923d6068 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    3. ID=ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    4. ID=2638f94b-0bc4-4c51-b2c2-aae911025668 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    5. ID=4f710668-d147-4c88-8239-ecee38118347 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    6. ID=bd079189-c0d9-449a-b5a2-61767d8aca33 | Score=0.3195 | DocID=RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    7. ID=45e1d982-dbae-4bda-9493-52ab74735756 | Score=0.3163 | DocID=DoR.RET.REC.12/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    8. ID=7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    9. ID=6ff8163a-0027-412b-a237-b343acbc7037 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT]    10. ID=78e63424-31da-4ff2-a793-3897c8a0ce69 | Score=0.3037 | DocID=DOR.CRE.REC.No.06/08.12.001/2023-24 | DocNum=unknown
2025-08-04 08:23:18,524 - INFO - [QDRANT] ➕ Added 10 new unique matches from document number search
2025-08-04 08:23:18,524 - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-04 08:23:18,524 - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-04 08:23:18,524 - INFO - [QDRANT] #1. Score: 0.3617 | ID: 8a26f3b3-4845-4e05-bca1-cebfaf86353a | DocID: RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Title: Exposures Of Scheduled Commercial Banks  Scbs  To Non-Banking Financial Companies  Nbfcs    Review Of Risk Weights
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Preview: <section data-bbox="[72.0, 18.**************, 219.*************, 39.*************]" data-page="1"><h...
2025-08-04 08:23:18,524 - INFO - [QDRANT] #2. Score: 0.3359 | ID: 0bceb959-f4eb-4ac7-879c-4a70923d6068 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:23:18,524 - INFO - [QDRANT] #3. Score: 0.3359 | ID: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:23:18,524 - INFO - [QDRANT] #4. Score: 0.3359 | ID: 2638f94b-0bc4-4c51-b2c2-aae911025668 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:23:18,524 - INFO - [QDRANT] #5. Score: 0.3359 | ID: 4f710668-d147-4c88-8239-ecee38118347 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:23:18,524 - INFO - [QDRANT] #6. Score: 0.3195 | ID: bd079189-c0d9-449a-b5a2-61767d8aca33 | DocID: RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26
2025-08-04 08:23:18,524 - INFO - [QDRANT]     Title: Basel III Capital Regulations – External Credit Assessment Institutions (ECAIs) – CareEdge Global IFSC Limited
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Preview: <p data-bbox="[71.99996948242188, 106.**************, 526.8359985351562, 258.1798400878906]" data-pa...
2025-08-04 08:23:18,525 - INFO - [QDRANT] #7. Score: 0.3163 | ID: 45e1d982-dbae-4bda-9493-52ab74735756 | DocID: DoR.RET.REC.12/12.01.001/2024-25
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:23:18,525 - INFO - [QDRANT] #8. Score: 0.3100 | ID: 7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:23:18,525 - INFO - [QDRANT] #9. Score: 0.3100 | ID: 6ff8163a-0027-412b-a237-b343acbc7037 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:23:18,525 - INFO - [QDRANT] #10. Score: 0.3037 | ID: 78e63424-31da-4ff2-a793-3897c8a0ce69 | DocID: DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:23:18,525 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:23:18,525 - INFO - [QDRANT] 🎯 Using extracted document IDs for filter: ['RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25', 'DoR.RET.REC.12/12.01.001/2024-25', 'RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25', 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26', 'DOR.CRE.REC.No.06/08.12.001/2023-24']
2025-08-04 08:23:18,525 - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-04 08:23:18,525 - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action_type', 'target_document', 'collection_name', 'use_vector_search', 'document_number', 'document_id']
2025-08-04 08:23:18,525 - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-04 08:23:18,525 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:23:18,525 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_number = Test Document Number
2025-08-04 08:23:18,525 - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 3 conditions
2025-08-04 08:23:18,525 - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='metadata.document_number', match=MatchValue(value='Test Document Number'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-04 08:23:18,607 - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-04 08:23:18,608 - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-04 08:23:18,608 - INFO - [QDRANT] 🔍 Using reliable fields for matching: document_id, document_number
2025-08-04 08:23:18,608 - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25", "timestamp": "2025-08-04T02:53:18.608315"}
2025-08-04 08:23:18,608 - INFO - Result: {'action': 'REMOVE_DOCUMENT', 'success': False, 'details': 'No matching documents found for ID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25', 'timestamp': '2025-08-04T02:53:18.608315'}
2025-08-04 08:23:18,608 - INFO - ✅ Removal action processed (may use vector search fallback)
2025-08-04 08:23:18,608 - INFO - ✅ PASSED KB Executor Removal
2025-08-04 08:23:18,608 - INFO - 
📋 Running test: Action Generation
2025-08-04 08:23:18,608 - INFO - 
🧪 Testing removal action generation...
2025-08-04 08:23:18,608 - INFO - ✅ Found RBI document IDs in content: ['RBI/FED/2023-24/15']
2025-08-04 08:23:18,608 - INFO -    📋 Extracted document ID: RBI/FED/2023-24/15
2025-08-04 08:23:18,608 - INFO - ✅ PASSED Action Generation
2025-08-04 08:23:18,608 - INFO - 
============================================================
2025-08-04 08:23:18,608 - INFO - 📊 TEST SUMMARY
2025-08-04 08:23:18,608 - INFO - ============================================================
2025-08-04 08:23:18,608 - INFO - ✅ PASS Removal Handler
2025-08-04 08:23:18,608 - INFO - ✅ PASS KB Executor Removal
2025-08-04 08:23:18,608 - INFO - ✅ PASS Action Generation
2025-08-04 08:23:18,608 - INFO - 
🎯 Overall: 3/3 tests passed
2025-08-04 08:23:18,608 - INFO - 🎉 All tests passed! Removal actions should be working.
2025-08-04 08:28:00,901 - INFO - 🚀 Starting removal action tests...
2025-08-04 08:28:00,902 - INFO - ============================================================
2025-08-04 08:28:00,902 - INFO - 
📋 Running test: Removal Handler
2025-08-04 08:28:00,902 - INFO - 🧪 Testing improved removal handler...
2025-08-04 08:28:00,902 - INFO - ✅ PASS Valid RBI document ID: 'RBI/FED/2024-25/17' -> valid=True, reason=''
2025-08-04 08:28:00,902 - WARNING - ⚠️ MANUAL_REVIEW_REQUIRED document ID detected: MANUAL_REVIEW_REQUIRED
2025-08-04 08:28:00,902 - INFO - ✅ PASS Should now allow with warning: 'MANUAL_REVIEW_REQUIRED' -> valid=True, reason='MANUAL_REVIEW_REQUIRED - will attempt vector search fallback'
2025-08-04 08:28:00,902 - INFO - ✅ PASS Valid DBR document ID: 'DBR.No.Ret.BC.78/12.02.001/2024-25' -> valid=True, reason=''
2025-08-04 08:28:00,902 - INFO - ✅ PASS Should reject preserved documents: 'PRESERVE_DOCUMENT' -> valid=False, reason='Documents marked for preservation cannot be removed'
2025-08-04 08:28:00,902 - INFO - ✅ PASS Should reject test documents: 'TEST_123' -> valid=False, reason='Test documents should be removed using test tools'
2025-08-04 08:28:00,902 - INFO - ✅ PASS Should reject empty document ID: '' -> valid=False, reason='Empty document ID'
2025-08-04 08:28:00,902 - INFO - 
🔧 Testing document ID normalization...
2025-08-04 08:28:00,902 - INFO - ✅ PASS 'RBI/FED/2024-25/17/' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:28:00,902 - INFO - ✅ PASS 'RBI\FED\2024-25\17' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:28:00,902 - INFO - ✅ PASS 'RBI/FED/2024-25/17 ' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:28:00,902 - INFO - ✅ PASSED Removal Handler
2025-08-04 08:28:00,902 - INFO - 
📋 Running test: KB Executor Removal
2025-08-04 08:28:00,902 - INFO - 
🧪 Testing KB executor removal logic...
2025-08-04 08:28:01,522 - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-04 08:28:03,998 - INFO - Configuration loaded successfully
2025-08-04 08:28:04,316 - INFO - Configuration loaded successfully
2025-08-04 08:28:05,770 - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-04 08:28:05,770 - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-04 08:28:05,787 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:28:05,792 - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-04 08:28:05,792 - INFO - ✅ QdrantClient initialized successfully
2025-08-04 08:28:06,666 - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-04 08:28:06,666 - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-04 08:28:06,666 - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-04 08:28:06,673 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082806.log
2025-08-04 08:28:06,673 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:28:06,747 - INFO - Use pytorch device_name: mps
2025-08-04 08:28:06,747 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:28:10,318 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:28:10,319 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:28:10,319 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:28:10,319 - INFO - 🎯 Testing removal action with MANUAL_REVIEW_REQUIRED...
2025-08-04 08:28:10,319 - INFO - Action: {'action_type': 'REMOVE_DOCUMENT', 'target_document': 'MANUAL_REVIEW_REQUIRED', 'collection_name': ['rbi_circular'], 'use_vector_search': True, 'document_number': 'Test Document Number'}
2025-08-04 08:28:10,319 - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action_type": "REMOVE_DOCUMENT", "target_document": "MANUAL_REVIEW_REQUIRED", "collection_name": ["rbi_circular"], "use_vector_search": true, "document_number": "Test Document Number"}
2025-08-04 08:28:10,319 - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_circular']
2025-08-04 08:28:10,319 - WARNING - ⚠️ MANUAL_REVIEW_REQUIRED document ID detected: MANUAL_REVIEW_REQUIRED
2025-08-04 08:28:10,319 - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-04 08:28:10,319 - INFO - [QDRANT] 🔍 Search parameters: document_id='MANUAL_REVIEW_REQUIRED', document_number='Test Document Number'
2025-08-04 08:28:10,321 - INFO - Use pytorch device_name: mps
2025-08-04 08:28:10,321 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:28:13,521 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:28:13,521 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:28:13,521 - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_circular']
2025-08-04 08:28:13,521 - INFO - [QDRANT] 🔍 SKIPPING DOCUMENT ID SEARCH (MANUAL_REVIEW_REQUIRED)
2025-08-04 08:28:13,521 - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT NUMBER: 'Test Document Number'
2025-08-04 08:28:13,618 - INFO - Searching metadata_vectors for 'Test Document Number' in field 'document_number'
2025-08-04 08:28:13,647 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:28:13,651 - INFO - Found 10 results for metadata search
2025-08-04 08:28:13,651 - INFO - [QDRANT] ✅ Found 10 matches by document number
2025-08-04 08:28:13,651 - INFO - [QDRANT] 📋 DOCUMENT NUMBER SEARCH RESULTS:
2025-08-04 08:28:13,651 - INFO - [QDRANT]    1. ID=8a26f3b3-4845-4e05-bca1-cebfaf86353a | Score=0.3617 | DocID=RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    2. ID=0bceb959-f4eb-4ac7-879c-4a70923d6068 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    3. ID=ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    4. ID=2638f94b-0bc4-4c51-b2c2-aae911025668 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    5. ID=4f710668-d147-4c88-8239-ecee38118347 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    6. ID=bd079189-c0d9-449a-b5a2-61767d8aca33 | Score=0.3195 | DocID=RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    7. ID=45e1d982-dbae-4bda-9493-52ab74735756 | Score=0.3163 | DocID=DoR.RET.REC.12/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    8. ID=7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    9. ID=6ff8163a-0027-412b-a237-b343acbc7037 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT]    10. ID=78e63424-31da-4ff2-a793-3897c8a0ce69 | Score=0.3037 | DocID=DOR.CRE.REC.No.06/08.12.001/2023-24 | DocNum=unknown
2025-08-04 08:28:13,651 - INFO - [QDRANT] ➕ Added 10 new unique matches from document number search
2025-08-04 08:28:13,651 - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-04 08:28:13,652 - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-04 08:28:13,652 - INFO - [QDRANT] #1. Score: 0.3617 | ID: 8a26f3b3-4845-4e05-bca1-cebfaf86353a | DocID: RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Exposures Of Scheduled Commercial Banks  Scbs  To Non-Banking Financial Companies  Nbfcs    Review Of Risk Weights
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <section data-bbox="[72.0, 18.**************, 219.*************, 39.*************]" data-page="1"><h...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #2. Score: 0.3359 | ID: 0bceb959-f4eb-4ac7-879c-4a70923d6068 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #3. Score: 0.3359 | ID: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #4. Score: 0.3359 | ID: 2638f94b-0bc4-4c51-b2c2-aae911025668 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #5. Score: 0.3359 | ID: 4f710668-d147-4c88-8239-ecee38118347 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #6. Score: 0.3195 | ID: bd079189-c0d9-449a-b5a2-61767d8aca33 | DocID: RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Basel III Capital Regulations – External Credit Assessment Institutions (ECAIs) – CareEdge Global IFSC Limited
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <p data-bbox="[71.99996948242188, 106.**************, 526.8359985351562, 258.1798400878906]" data-pa...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #7. Score: 0.3163 | ID: 45e1d982-dbae-4bda-9493-52ab74735756 | DocID: DoR.RET.REC.12/12.01.001/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #8. Score: 0.3100 | ID: 7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #9. Score: 0.3100 | ID: 6ff8163a-0027-412b-a237-b343acbc7037 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:28:13,652 - INFO - [QDRANT] #10. Score: 0.3037 | ID: 78e63424-31da-4ff2-a793-3897c8a0ce69 | DocID: DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:28:13,652 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:28:13,652 - WARNING - [QDRANT] ⚠️ No similar matches found for MANUAL_REVIEW_REQUIRED, keeping original target
2025-08-04 08:28:13,653 - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-04 08:28:13,653 - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action_type', 'target_document', 'collection_name', 'use_vector_search', 'document_number', 'document_id', 'filter_fields']
2025-08-04 08:28:13,653 - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'metadata.document_id': 'MANUAL_REVIEW_REQUIRED'}
2025-08-04 08:28:13,653 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding filter: metadata.document_id = MANUAL_REVIEW_REQUIRED
2025-08-04 08:28:13,653 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_number = Test Document Number
2025-08-04 08:28:13,653 - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-04 08:28:13,653 - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='MANUAL_REVIEW_REQUIRED'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='metadata.document_number', match=MatchValue(value='Test Document Number'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-04 08:28:13,653 - INFO - [QDRANT] REMOVE filter fields: {"metadata.document_id": "MANUAL_REVIEW_REQUIRED"}
2025-08-04 08:28:13,735 - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-04 08:28:13,735 - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-04 08:28:13,735 - WARNING - ⚠️ MANUAL_REVIEW_REQUIRED document ID detected: MANUAL_REVIEW_REQUIRED
2025-08-04 08:28:13,735 - INFO - [QDRANT] 🔍 Using reliable fields for matching: document_id, document_number
2025-08-04 08:28:13,735 - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "MANUAL_REVIEW_REQUIRED documents cannot be removed automatically", "timestamp": "2025-08-04T02:58:13.735734"}
2025-08-04 08:28:13,736 - INFO - Result: {'action': 'REMOVE_DOCUMENT', 'success': False, 'details': 'MANUAL_REVIEW_REQUIRED documents cannot be removed automatically', 'timestamp': '2025-08-04T02:58:13.735734'}
2025-08-04 08:28:13,736 - ERROR - ❌ Removal still being rejected for MANUAL_REVIEW_REQUIRED
2025-08-04 08:28:13,736 - INFO - ❌ FAILED KB Executor Removal
2025-08-04 08:28:13,736 - INFO - 
📋 Running test: Action Generation
2025-08-04 08:28:13,736 - INFO - 
🧪 Testing removal action generation...
2025-08-04 08:28:13,736 - INFO - ✅ Found RBI document IDs in content: ['RBI/FED/2023-24/15']
2025-08-04 08:28:13,736 - INFO -    📋 Extracted document ID: RBI/FED/2023-24/15
2025-08-04 08:28:13,736 - INFO - ✅ PASSED Action Generation
2025-08-04 08:28:13,736 - INFO - 
============================================================
2025-08-04 08:28:13,736 - INFO - 📊 TEST SUMMARY
2025-08-04 08:28:13,736 - INFO - ============================================================
2025-08-04 08:28:13,736 - INFO - ✅ PASS Removal Handler
2025-08-04 08:28:13,736 - INFO - ❌ FAIL KB Executor Removal
2025-08-04 08:28:13,736 - INFO - ✅ PASS Action Generation
2025-08-04 08:28:13,736 - INFO - 
🎯 Overall: 2/3 tests passed
2025-08-04 08:28:13,736 - WARNING - ⚠️ Some tests failed. Check the logs for details.
2025-08-04 08:28:50,618 - INFO - 🚀 Starting removal action tests...
2025-08-04 08:28:50,619 - INFO - ============================================================
2025-08-04 08:28:50,619 - INFO - 
📋 Running test: Removal Handler
2025-08-04 08:28:50,619 - INFO - 🧪 Testing improved removal handler...
2025-08-04 08:28:50,619 - INFO - ✅ PASS Valid RBI document ID: 'RBI/FED/2024-25/17' -> valid=True, reason=''
2025-08-04 08:28:50,619 - WARNING - ⚠️ MANUAL_REVIEW_REQUIRED document ID detected: MANUAL_REVIEW_REQUIRED
2025-08-04 08:28:50,619 - INFO - ✅ PASS Should now allow with warning: 'MANUAL_REVIEW_REQUIRED' -> valid=True, reason='MANUAL_REVIEW_REQUIRED - will attempt vector search fallback'
2025-08-04 08:28:50,619 - INFO - ✅ PASS Valid DBR document ID: 'DBR.No.Ret.BC.78/12.02.001/2024-25' -> valid=True, reason=''
2025-08-04 08:28:50,619 - INFO - ✅ PASS Should reject preserved documents: 'PRESERVE_DOCUMENT' -> valid=False, reason='Documents marked for preservation cannot be removed'
2025-08-04 08:28:50,619 - INFO - ✅ PASS Should reject test documents: 'TEST_123' -> valid=False, reason='Test documents should be removed using test tools'
2025-08-04 08:28:50,619 - INFO - ✅ PASS Should reject empty document ID: '' -> valid=False, reason='Empty document ID'
2025-08-04 08:28:50,619 - INFO - 
🔧 Testing document ID normalization...
2025-08-04 08:28:50,619 - INFO - ✅ PASS 'RBI/FED/2024-25/17/' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:28:50,619 - INFO - ✅ PASS 'RBI\FED\2024-25\17' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:28:50,619 - INFO - ✅ PASS 'RBI/FED/2024-25/17 ' -> 'RBI/FED/2024-25/17' (expected: 'RBI/FED/2024-25/17')
2025-08-04 08:28:50,619 - INFO - ✅ PASSED Removal Handler
2025-08-04 08:28:50,619 - INFO - 
📋 Running test: KB Executor Removal
2025-08-04 08:28:50,619 - INFO - 
🧪 Testing KB executor removal logic...
2025-08-04 08:28:51,158 - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-04 08:28:53,164 - INFO - Configuration loaded successfully
2025-08-04 08:28:53,478 - INFO - Configuration loaded successfully
2025-08-04 08:28:54,860 - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-04 08:28:54,860 - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-04 08:28:54,874 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:28:54,877 - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-04 08:28:54,878 - INFO - ✅ QdrantClient initialized successfully
2025-08-04 08:28:55,907 - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-04 08:28:55,908 - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-04 08:28:55,908 - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-04 08:28:55,915 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082855.log
2025-08-04 08:28:55,915 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:28:55,990 - INFO - Use pytorch device_name: mps
2025-08-04 08:28:55,990 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:28:59,607 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:28:59,608 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:28:59,608 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:28:59,608 - INFO - 🎯 Testing removal action with real document ID...
2025-08-04 08:28:59,608 - INFO - Action: {'action_type': 'REMOVE_DOCUMENT', 'target_document': 'RBI/FED/2023-24/15', 'collection_name': ['rbi_circular'], 'use_vector_search': True, 'document_id': 'RBI/FED/2023-24/15', 'document_number': 'Test Document Number'}
2025-08-04 08:28:59,608 - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action_type": "REMOVE_DOCUMENT", "target_document": "RBI/FED/2023-24/15", "collection_name": ["rbi_circular"], "use_vector_search": true, "document_id": "RBI/FED/2023-24/15", "document_number": "Test Document Number"}
2025-08-04 08:28:59,608 - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_circular']
2025-08-04 08:28:59,608 - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-04 08:28:59,608 - INFO - [QDRANT] 🔍 Search parameters: document_id='RBI/FED/2023-24/15', document_number='Test Document Number'
2025-08-04 08:28:59,609 - INFO - Use pytorch device_name: mps
2025-08-04 08:28:59,609 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:02,879 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:02,879 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:29:02,879 - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_circular']
2025-08-04 08:29:02,879 - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT ID: 'RBI/FED/2023-24/15'
2025-08-04 08:29:02,960 - INFO - Searching metadata_vectors for 'RBI/FED/2023-24/15' in field 'document_id'
2025-08-04 08:29:02,981 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:29:02,982 - INFO - Found 10 results for metadata search
2025-08-04 08:29:02,982 - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT NUMBER: 'Test Document Number'
2025-08-04 08:29:03,016 - INFO - Searching metadata_vectors for 'Test Document Number' in field 'document_number'
2025-08-04 08:29:03,026 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:29:03,028 - INFO - Found 10 results for metadata search
2025-08-04 08:29:03,028 - INFO - [QDRANT] ✅ Found 10 matches by document number
2025-08-04 08:29:03,028 - INFO - [QDRANT] 📋 DOCUMENT NUMBER SEARCH RESULTS:
2025-08-04 08:29:03,028 - INFO - [QDRANT]    1. ID=8a26f3b3-4845-4e05-bca1-cebfaf86353a | Score=0.3617 | DocID=RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    2. ID=0bceb959-f4eb-4ac7-879c-4a70923d6068 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    3. ID=ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    4. ID=2638f94b-0bc4-4c51-b2c2-aae911025668 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    5. ID=4f710668-d147-4c88-8239-ecee38118347 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    6. ID=bd079189-c0d9-449a-b5a2-61767d8aca33 | Score=0.3195 | DocID=RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    7. ID=45e1d982-dbae-4bda-9493-52ab74735756 | Score=0.3163 | DocID=DoR.RET.REC.12/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    8. ID=7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    9. ID=6ff8163a-0027-412b-a237-b343acbc7037 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT]    10. ID=78e63424-31da-4ff2-a793-3897c8a0ce69 | Score=0.3037 | DocID=DOR.CRE.REC.No.06/08.12.001/2023-24 | DocNum=unknown
2025-08-04 08:29:03,028 - INFO - [QDRANT] ➕ Added 10 new unique matches from document number search
2025-08-04 08:29:03,028 - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-04 08:29:03,028 - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-04 08:29:03,028 - INFO - [QDRANT] #1. Score: 0.3617 | ID: 8a26f3b3-4845-4e05-bca1-cebfaf86353a | DocID: RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25
2025-08-04 08:29:03,028 - INFO - [QDRANT]     Title: Exposures Of Scheduled Commercial Banks  Scbs  To Non-Banking Financial Companies  Nbfcs    Review Of Risk Weights
2025-08-04 08:29:03,028 - INFO - [QDRANT]     Preview: <section data-bbox="[72.0, 18.**************, 219.*************, 39.*************]" data-page="1"><h...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #2. Score: 0.3359 | ID: 0bceb959-f4eb-4ac7-879c-4a70923d6068 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #3. Score: 0.3359 | ID: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #4. Score: 0.3359 | ID: 2638f94b-0bc4-4c51-b2c2-aae911025668 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #5. Score: 0.3359 | ID: 4f710668-d147-4c88-8239-ecee38118347 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #6. Score: 0.3195 | ID: bd079189-c0d9-449a-b5a2-61767d8aca33 | DocID: RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: Basel III Capital Regulations – External Credit Assessment Institutions (ECAIs) – CareEdge Global IFSC Limited
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: <p data-bbox="[71.99996948242188, 106.**************, 526.8359985351562, 258.1798400878906]" data-pa...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #7. Score: 0.3163 | ID: 45e1d982-dbae-4bda-9493-52ab74735756 | DocID: DoR.RET.REC.12/12.01.001/2024-25
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #8. Score: 0.3100 | ID: 7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #9. Score: 0.3100 | ID: 6ff8163a-0027-412b-a237-b343acbc7037 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:29:03,029 - INFO - [QDRANT] #10. Score: 0.3037 | ID: 78e63424-31da-4ff2-a793-3897c8a0ce69 | DocID: DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:03,029 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:03,030 - WARNING - [QDRANT] ⚠️ No similar matches found for RBI/FED/2023-24/15, keeping original target
2025-08-04 08:29:03,030 - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-04 08:29:03,030 - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action_type', 'target_document', 'collection_name', 'use_vector_search', 'document_id', 'document_number', 'filter_fields']
2025-08-04 08:29:03,030 - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'metadata.document_id': 'RBI/FED/2023-24/15'}
2025-08-04 08:29:03,030 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding filter: metadata.document_id = RBI/FED/2023-24/15
2025-08-04 08:29:03,030 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = RBI/FED/2023-24/15
2025-08-04 08:29:03,030 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_number = Test Document Number
2025-08-04 08:29:03,030 - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 4 conditions
2025-08-04 08:29:03,030 - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='metadata.document_number', match=MatchValue(value='Test Document Number'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-04 08:29:03,030 - INFO - [QDRANT] REMOVE filter fields: {"metadata.document_id": "RBI/FED/2023-24/15"}
2025-08-04 08:29:03,106 - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-04 08:29:03,106 - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-04 08:29:03,106 - INFO - [QDRANT] 🔍 Using reliable fields for matching: document_id, document_number
2025-08-04 08:29:03,106 - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/FED/2023-24/15", "timestamp": "2025-08-04T02:59:03.106739"}
2025-08-04 08:29:03,107 - INFO - Result: {'action': 'REMOVE_DOCUMENT', 'success': False, 'details': 'No matching documents found for ID: RBI/FED/2023-24/15', 'timestamp': '2025-08-04T02:59:03.106739'}
2025-08-04 08:29:03,107 - INFO - ✅ Removal action processed successfully
2025-08-04 08:29:03,107 - INFO - ✅ PASSED KB Executor Removal
2025-08-04 08:29:03,107 - INFO - 
📋 Running test: Action Generation
2025-08-04 08:29:03,107 - INFO - 
🧪 Testing removal action generation...
2025-08-04 08:29:03,107 - INFO - ✅ Found RBI document IDs in content: ['RBI/FED/2023-24/15']
2025-08-04 08:29:03,107 - INFO -    📋 Extracted document ID: RBI/FED/2023-24/15
2025-08-04 08:29:03,107 - INFO - ✅ PASSED Action Generation
2025-08-04 08:29:03,107 - INFO - 
============================================================
2025-08-04 08:29:03,107 - INFO - 📊 TEST SUMMARY
2025-08-04 08:29:03,107 - INFO - ============================================================
2025-08-04 08:29:03,107 - INFO - ✅ PASS Removal Handler
2025-08-04 08:29:03,107 - INFO - ✅ PASS KB Executor Removal
2025-08-04 08:29:03,107 - INFO - ✅ PASS Action Generation
2025-08-04 08:29:03,107 - INFO - 
🎯 Overall: 3/3 tests passed
2025-08-04 08:29:03,107 - INFO - 🎉 All tests passed! Removal actions should be working.
