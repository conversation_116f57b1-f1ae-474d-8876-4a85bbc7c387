"""
Production-ready fixes for manual_test.py
Apply these patterns to make the code production-grade.
"""

import hashlib
import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
from enum import Enum

# 1. Use secure hashing
def secure_hash(data: str) -> str:
    """Use SHA256 instead of MD5"""
    return hashlib.sha256(data.encode()).hexdigest()

# 2. Use timezone-aware datetimes
def get_utc_now() -> datetime:
    """Get current UTC time with timezone info"""
    return datetime.now(timezone.utc)

# 3. Proper resource management
@contextmanager
def managed_temp_file(content: bytes):
    """Context manager for temporary files"""
    import tempfile
    import os
    
    temp_file = None
    try:
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        temp_file.write(content)
        temp_file.flush()
        yield temp_file.name
    finally:
        if temp_file:
            temp_file.close()
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)

# 4. Use enums for consistency
class ComplexityLevel(Enum):
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    CRITICAL = "critical"

class ActionType(Enum):
    CREATE = "CREATE"
    UPDATE = "UPDATE"
    REMOVE = "REMOVE"
    SUPERSEDE = "SUPERSEDE"

# 5. Specific exception handling
class PipelineError(Exception):
    """Base exception for pipeline errors"""
    pass

class LLMError(PipelineError):
    """LLM-specific errors"""
    pass

class DocumentProcessingError(PipelineError):
    """Document processing errors"""
    pass

# 6. Proper logging with context
def setup_production_logger(name: str) -> logging.Logger:
    """Setup production-ready logger"""
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger

# 7. Efficient string operations
def format_log_message(template: str, **kwargs) -> str:
    """Use f-strings for efficient formatting"""
    return template.format(**kwargs)

# 8. Batch API calls
async def batch_llm_calls(prompts: List[str], batch_size: int = 5) -> List[str]:
    """Batch multiple LLM calls for efficiency"""
    import asyncio
    
    results = []
    for i in range(0, len(prompts), batch_size):
        batch = prompts[i:i + batch_size]
        # Process batch concurrently
        batch_results = await asyncio.gather(*[
            process_single_prompt(prompt) for prompt in batch
        ])
        results.extend(batch_results)
    
    return results

async def process_single_prompt(prompt: str) -> str:
    """Process single prompt - placeholder"""
    # Your LLM call logic here
    return "result"

# 9. Configuration management
class ProductionConfig:
    """Production configuration"""
    
    def __init__(self):
        self.max_retries = 3
        self.timeout_seconds = 30
        self.batch_size = 5
        self.log_level = logging.INFO
        
    @classmethod
    def from_env(cls) -> 'ProductionConfig':
        """Load config from environment"""
        import os
        config = cls()
        config.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        config.timeout_seconds = int(os.getenv('TIMEOUT_SECONDS', '30'))
        return config

# 10. Health checks
class HealthChecker:
    """Production health checks"""
    
    def __init__(self):
        self.checks = []
    
    def add_check(self, name: str, check_func):
        """Add health check"""
        self.checks.append((name, check_func))
    
    def run_checks(self) -> Dict[str, bool]:
        """Run all health checks"""
        results = {}
        for name, check_func in self.checks:
            try:
                results[name] = check_func()
            except Exception as e:
                logging.error(f"Health check {name} failed: {e}")
                results[name] = False
        return results