#!/usr/bin/env python3
"""
Simple Test Runner for Pipeline Tests

This script runs pipeline tests through a simplified version that focuses on
KB action extraction and manual verification without heavy dependencies.
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import traceback
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimplePipelineTest:
    """Simplified pipeline test that focuses on KB action extraction"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_API_KEYS', '').split('|')[0]
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
    
    def analyze_notification_simple(self, title: str, summary: str, pdf_link: str) -> Dict:
        """Simple notification analysis using OpenAI"""
        try:
            import openai
            client = openai.OpenAI(api_key=self.openai_api_key)
            
            prompt = f"""
You are an expert RBI regulatory analyst. Analyze this notification and determine:

1. Category (regulatory, informational, administrative, withdrawn)
2. Whether it affects existing documents
3. What KB actions are needed
4. Confidence level

Notification:
Title: {title}
Summary: {summary}
PDF Link: {pdf_link}

Respond in JSON format with:
{{
    "category": "regulatory|informational|administrative|withdrawn",
    "affects_existing_documents": true|false,
    "kb_actions_needed": ["ADD_DOCUMENT", "REMOVE_DOCUMENT", "UPDATE_DOCUMENT"],
    "confidence": "high|medium|low",
    "reasoning": "explanation of analysis",
    "target_collection": "rbi_circular|rbi_master_circular|rbi_master_direction|rbi_other"
}}
"""
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response.choices[0].message.content)
            logger.info(f"✅ Analysis completed: {result.get('category', 'unknown')} ({result.get('confidence', 'unknown')})")
            return result
            
        except Exception as e:
            logger.error(f"❌ Analysis failed: {e}")
            return {
                "category": "unknown",
                "affects_existing_documents": False,
                "kb_actions_needed": [],
                "confidence": "low",
                "reasoning": f"Analysis failed: {str(e)}",
                "target_collection": "rbi_other"
            }
    
    def extract_kb_actions_simple(self, title: str, summary: str, analysis: Dict) -> List[Dict]:
        """Extract KB actions using OpenAI"""
        try:
            import openai
            client = openai.OpenAI(api_key=self.openai_api_key)
            
            prompt = f"""
You are an expert RBI knowledge base manager. Based on this notification analysis, determine specific KB actions needed.

Notification:
Title: {title}
Summary: {summary}
Analysis Category: {analysis.get('category', 'unknown')}
Affects Existing: {analysis.get('affects_existing_documents', False)}

Extract specific actions needed. For each action, provide:
- action_type: ADD_DOCUMENT, REMOVE_DOCUMENT, UPDATE_DOCUMENT, or NO_ACTION
- target_document: specific document ID or title
- collection: rbi_circular, rbi_master_circular, rbi_master_direction, or rbi_other
- priority: HIGH, MEDIUM, or LOW
- reasoning: why this action is needed
- details: specific details about the action

Respond in JSON format:
{{
    "actions": [
        {{
            "action_type": "ADD_DOCUMENT",
            "target_document": "document identifier",
            "collection": "rbi_circular",
            "priority": "MEDIUM",
            "reasoning": "explanation",
            "details": "specific details"
        }}
    ]
}}
"""
            
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            result = json.loads(response.choices[0].message.content)
            actions = result.get('actions', [])
            logger.info(f"✅ Extracted {len(actions)} KB actions")
            return actions
            
        except Exception as e:
            logger.error(f"❌ KB action extraction failed: {e}")
            return []

def run_simple_test(category_name: str, max_tests: int = 3):
    """Run simple tests for manual verification"""
    
    # Initialize simple pipeline
    logger.info("🚀 Initializing simple pipeline...")
    try:
        pipeline = SimplePipelineTest()
        logger.info("✅ Simple pipeline initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize pipeline: {e}")
        return
    
    # Load test file
    test_file = Path(f"pipeline_tests/{category_name}.json")
    if not test_file.exists():
        logger.error(f"❌ Test file not found: {test_file}")
        return
    
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    # Limit number of tests
    test_data = test_data[:max_tests]
    logger.info(f"📁 Running {len(test_data)} tests from {category_name}")
    
    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = Path(f"simple_test_outputs_{category_name}_{timestamp}")
    output_dir.mkdir(exist_ok=True)
    
    results = []
    
    for i, notification in enumerate(test_data):
        logger.info(f"\n{'='*50}")
        logger.info(f"🧪 Test {i+1}/{len(test_data)}: {notification.get('title', 'Unknown')[:50]}...")
        
        try:
            # Extract notification details
            title = notification.get('title', '')
            summary = notification.get('summary', '')
            pdf_link = notification.get('pdf_link', '')
            expected_action = notification.get('action', '')
            
            # Step 1: Analyze notification
            logger.info("📊 Analyzing notification...")
            analysis = pipeline.analyze_notification_simple(title, summary, pdf_link)
            
            # Step 2: Extract KB actions
            logger.info("🎯 Extracting KB actions...")
            kb_actions = pipeline.extract_kb_actions_simple(title, summary, analysis)
            
            # Compile result
            result = {
                'test_index': i + 1,
                'notification_id': notification.get('notification_id', ''),
                'title': title,
                'expected_action': expected_action,
                'pdf_link': pdf_link,
                'analysis': analysis,
                'kb_actions': kb_actions,
                'status': 'success',
                'processing_timestamp': datetime.now().isoformat()
            }
            
            results.append(result)
            
            # Save individual result
            individual_file = output_dir / f"test_{i+1}_{notification.get('notification_id', 'unknown')}.json"
            with open(individual_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ Test {i+1} completed successfully")
            
            # Print summary for manual verification
            print(f"\n📋 MANUAL VERIFICATION - Test {i+1}:")
            print(f"Title: {title}")
            print(f"Expected Action: {expected_action}")
            print(f"LLM Category: {analysis.get('category', 'N/A')}")
            print(f"Affects Existing: {analysis.get('affects_existing_documents', 'N/A')}")
            print(f"KB Actions Found: {len(kb_actions)}")
            
            for j, action in enumerate(kb_actions):
                print(f"  Action {j+1}: {action.get('action_type', 'N/A')} -> {action.get('target_document', 'N/A')} [{action.get('priority', 'N/A')}]")
                print(f"    Collection: {action.get('collection', 'N/A')}")
                print(f"    Reasoning: {action.get('reasoning', 'N/A')[:100]}...")
            
            print(f"  Analysis Reasoning: {analysis.get('reasoning', 'N/A')[:100]}...")
            
        except Exception as e:
            logger.error(f"❌ Test {i+1} failed: {e}")
            logger.error(traceback.format_exc())
            
            result = {
                'test_index': i + 1,
                'notification_id': notification.get('notification_id', ''),
                'title': notification.get('title', ''),
                'expected_action': notification.get('action', ''),
                'error': str(e),
                'error_traceback': traceback.format_exc(),
                'status': 'error',
                'processing_timestamp': datetime.now().isoformat()
            }
            results.append(result)
    
    # Save summary
    summary = {
        'category': category_name,
        'total_tests': len(test_data),
        'successful_tests': len([r for r in results if r.get('status') == 'success']),
        'failed_tests': len([r for r in results if r.get('status') == 'error']),
        'total_kb_actions': sum(len(r.get('kb_actions', [])) for r in results if r.get('status') == 'success'),
        'results': results,
        'processing_timestamp': datetime.now().isoformat()
    }
    
    summary_file = output_dir / "summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
    
    # Generate verification report
    report_file = output_dir / "VERIFICATION_REPORT.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(f"# {category_name.title()} Test Verification Report\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Summary\n\n")
        f.write(f"- **Total Tests:** {summary['total_tests']}\n")
        f.write(f"- **Successful:** {summary['successful_tests']}\n")
        f.write(f"- **Failed:** {summary['failed_tests']}\n")
        f.write(f"- **Total KB Actions:** {summary['total_kb_actions']}\n\n")
        
        f.write("## Manual Verification Checklist\n\n")
        f.write("For each test result, verify:\n\n")
        f.write("1. **Category Classification**: Does the LLM category match the expected action type?\n")
        f.write("2. **KB Actions**: Are the extracted actions appropriate for the notification?\n")
        f.write("3. **Target Documents**: Are document identifiers reasonable?\n")
        f.write("4. **Collections**: Are documents assigned to correct collections?\n")
        f.write("5. **Priority**: Are action priorities appropriate?\n\n")
        
        f.write("## Test Results\n\n")
        for result in results:
            if result.get('status') == 'success':
                f.write(f"### ✅ Test {result['test_index']}: {result['title'][:50]}...\n\n")
                f.write(f"**Expected Action:** {result['expected_action']}\n")
                f.write(f"**LLM Category:** {result['analysis'].get('category', 'N/A')}\n")
                f.write(f"**Affects Existing:** {result['analysis'].get('affects_existing_documents', 'N/A')}\n")
                f.write(f"**Confidence:** {result['analysis'].get('confidence', 'N/A')}\n\n")
                
                f.write("**KB Actions:**\n")
                for i, action in enumerate(result.get('kb_actions', [])):
                    f.write(f"{i+1}. **{action.get('action_type', 'N/A')}** -> `{action.get('target_document', 'N/A')}`\n")
                    f.write(f"   - Collection: {action.get('collection', 'N/A')}\n")
                    f.write(f"   - Priority: {action.get('priority', 'N/A')}\n")
                    f.write(f"   - Reasoning: {action.get('reasoning', 'N/A')}\n\n")
                
                f.write(f"**Analysis Reasoning:** {result['analysis'].get('reasoning', 'N/A')}\n\n")
                f.write("---\n\n")
            else:
                f.write(f"### ❌ Test {result['test_index']} (FAILED)\n")
                f.write(f"**Title:** {result['title']}\n")
                f.write(f"**Error:** {result.get('error', 'Unknown error')}\n\n")
    
    # Generate CSV for easy review
    csv_file = output_dir / "verification_checklist.csv"
    with open(csv_file, 'w', encoding='utf-8') as f:
        f.write("Test,Title,Expected_Action,LLM_Category,KB_Actions_Count,Status,Verified\n")
        for result in results:
            if result.get('status') == 'success':
                f.write(f"{result['test_index']},\"{result['title'][:50]}\",{result['expected_action']},{result['analysis'].get('category', 'N/A')},{len(result.get('kb_actions', []))},SUCCESS,\n")
            else:
                f.write(f"{result['test_index']},\"{result['title'][:50]}\",{result['expected_action']},ERROR,0,FAILED,\n")
    
    logger.info(f"\n🎉 Test run completed!")
    logger.info(f"📊 Results: {summary['successful_tests']}/{summary['total_tests']} successful")
    logger.info(f"📁 Output saved to: {output_dir}")
    logger.info(f"📋 Review {report_file} for manual verification")
    logger.info(f"📊 Use {csv_file} for checklist tracking")

def main():
    if len(sys.argv) < 2:
        print("Usage: python simple_test_runner.py <category_name> [max_tests]")
        print("Available categories:")
        test_dir = Path("pipeline_tests")
        for test_file in test_dir.glob("*.json"):
            print(f"  - {test_file.stem}")
        sys.exit(1)
    
    category_name = sys.argv[1]
    max_tests = int(sys.argv[2]) if len(sys.argv) > 2 else 3
    
    run_simple_test(category_name, max_tests)

if __name__ == "__main__":
    main()