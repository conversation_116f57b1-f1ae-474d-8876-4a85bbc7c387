#!/usr/bin/env python3
"""
Pipeline optimization script to ensure maximum performance.
"""

import os
import sys
import logging
from pathlib import Path

def optimize_logging():
    """Optimize logging configuration for performance"""
    print("🔧 Optimizing logging configuration...")
    
    # Clear any existing handlers to prevent memory leaks
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        handler.close()
    
    # Set optimal logging levels
    logging.getLogger('openai').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    print("✅ Logging optimized")

def optimize_environment():
    """Set optimal environment variables"""
    print("🔧 Optimizing environment variables...")
    
    # Set optimal environment for performance
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'  # Avoid tokenizer warnings
    os.environ['PYTHONUNBUFFERED'] = '1'  # Immediate output
    
    # Optimize Python garbage collection
    import gc
    gc.set_threshold(700, 10, 10)  # More aggressive garbage collection
    
    print("✅ Environment optimized")

def check_dependencies():
    """Check that all dependencies are properly installed"""
    print("🔧 Checking dependencies...")
    
    required_modules = [
        'qdrant_client',
        'openai',
        'sentence_transformers',
        'langchain',
        'pydantic'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        print(f"❌ Missing dependencies: {missing}")
        return False
    else:
        print("✅ All dependencies available")
        return True

def optimize_memory():
    """Optimize memory usage"""
    print("🔧 Optimizing memory usage...")
    
    import gc
    
    # Force garbage collection
    collected = gc.collect()
    print(f"✅ Memory optimized (collected {collected} objects)")

def create_performance_config():
    """Create optimal configuration file"""
    print("🔧 Creating performance configuration...")
    
    config = {
        "logging": {
            "console_level": "WARNING",
            "file_level": "INFO",
            "flush_interval": 10
        },
        "processing": {
            "batch_size": 10,
            "progress_interval": 10,
            "max_retries": 3
        },
        "memory": {
            "gc_threshold": [700, 10, 10],
            "clear_cache_interval": 50
        }
    }
    
    import json
    with open('performance_config.json', 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Performance config created")

def run_performance_test():
    """Run a quick performance test"""
    print("🔧 Running performance test...")
    
    import time
    start_time = time.time()
    
    # Test logging performance
    logger = logging.getLogger('test')
    for i in range(1000):
        logger.info(f"Test message {i}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    if duration < 1.0:
        print(f"✅ Performance test: EXCELLENT ({duration:.3f}s for 1000 messages)")
        return True
    elif duration < 3.0:
        print(f"⚡ Performance test: GOOD ({duration:.3f}s for 1000 messages)")
        return True
    else:
        print(f"⚠️ Performance test: SLOW ({duration:.3f}s for 1000 messages)")
        return False

def main():
    """Run all optimizations"""
    print("🚀 Starting pipeline optimization...")
    print("=" * 50)
    
    optimizations = [
        ("Logging", optimize_logging),
        ("Environment", optimize_environment),
        ("Dependencies", check_dependencies),
        ("Memory", optimize_memory),
        ("Configuration", create_performance_config),
        ("Performance Test", run_performance_test),
    ]
    
    results = []
    for name, func in optimizations:
        print(f"\n📋 {name}...")
        try:
            result = func()
            results.append((name, result if result is not None else True))
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 OPTIMIZATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {name}")
    
    print(f"\n🎯 Optimization Score: {passed}/{total} ({(passed/total*100):.1f}%)")
    
    if passed == total:
        print("🎉 PIPELINE FULLY OPTIMIZED!")
        print("\n🚀 Ready to run: python manual_test.py")
    else:
        print("⚠️ Some optimizations failed. Pipeline will still work but may be slower.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
