"""
Knowledge Base Operations Dashboard
==================================

Real-time monitoring dashboard for KB operations, filters, and collection changes.
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
from kb_tracking_system import kb_tracker
import logging

class KBDashboard:
    """
    Real-time dashboard for monitoring KB operations
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def print_header(self):
        """Print dashboard header"""
        print("\n" + "="*80)
        print("🔍 KNOWLEDGE BASE OPERATIONS DASHBOARD")
        print("="*80)
        print(f"📅 Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
    
    def print_active_actions(self):
        """Display currently active actions"""
        print("\n🎯 ACTIVE ACTIONS")
        print("-" * 50)
        
        if not kb_tracker.active_actions:
            print("   No active actions")
            return
        
        for action_id, tracker in kb_tracker.active_actions.items():
            duration = self._calculate_duration(tracker.execution_start)
            print(f"   🔄 {tracker.action_type} ({action_id[:8]}...)")
            print(f"      Target: {tracker.target_document}")
            print(f"      Collection: {tracker.collection}")
            print(f"      Duration: {duration}")
            print(f"      Filters: {len(tracker.filter_conditions)} conditions")
            
            # Show filter details
            for condition in tracker.filter_conditions[:3]:  # Show first 3
                value_str = str(condition.value)[:30] + "..." if len(str(condition.value)) > 30 else str(condition.value)
                print(f"         📋 {condition.field} = {value_str} ({condition.source})")
            
            if len(tracker.filter_conditions) > 3:
                print(f"         ... and {len(tracker.filter_conditions) - 3} more")
            print()
    
    def print_recent_actions(self, limit: int = 10):
        """Display recent completed actions"""
        print(f"\n📊 RECENT ACTIONS (Last {limit})")
        print("-" * 50)
        
        recent_actions = sorted(
            kb_tracker.completed_actions,
            key=lambda x: x.execution_end or x.execution_start,
            reverse=True
        )[:limit]
        
        if not recent_actions:
            print("   No completed actions")
            return
        
        for tracker in recent_actions:
            status = "✅" if tracker.success else "❌"
            duration = self._calculate_duration(tracker.execution_start, tracker.execution_end)
            
            print(f"   {status} {tracker.action_type} ({tracker.action_id[:8]}...)")
            print(f"      Target: {tracker.target_document}")
            print(f"      Collection: {tracker.collection}")
            print(f"      Duration: {duration}")
            print(f"      Documents affected: {tracker.documents_affected}")
            
            if tracker.error_message:
                print(f"      ❌ Error: {tracker.error_message[:50]}...")
            
            # Show key filter used
            if tracker.filter_conditions:
                main_filter = tracker.filter_conditions[0]
                value_str = str(main_filter.value)[:20] + "..." if len(str(main_filter.value)) > 20 else str(main_filter.value)
                print(f"      🔍 Main filter: {main_filter.field} = {value_str}")
            print()
    
    def print_collection_stats(self):
        """Display collection statistics"""
        print("\n📈 COLLECTION STATISTICS")
        print("-" * 50)
        
        for name, stats in kb_tracker.collection_stats.items():
            print(f"   📚 {name}")
            print(f"      Total documents: {stats.total_documents}")
            print(f"      Added: {stats.documents_added}")
            print(f"      Removed: {stats.documents_removed}")
            print(f"      Updated: {stats.documents_updated}")
            print(f"      Last operation: {stats.last_operation}")
            print()
    
    def print_filter_analysis(self):
        """Analyze filter usage patterns"""
        print("\n🔍 FILTER ANALYSIS")
        print("-" * 50)
        
        # Collect filter statistics
        filter_stats = {}
        source_stats = {}
        
        all_actions = kb_tracker.completed_actions + list(kb_tracker.active_actions.values())
        
        for tracker in all_actions:
            for condition in tracker.filter_conditions:
                # Count field usage
                field = condition.field
                filter_stats[field] = filter_stats.get(field, 0) + 1
                
                # Count source usage
                source = condition.source
                source_stats[source] = source_stats.get(source, 0) + 1
        
        print("   Most used filter fields:")
        for field, count in sorted(filter_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
            print(f"      📋 {field}: {count} times")
        
        print("\n   Filter sources:")
        for source, count in sorted(source_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"      🎯 {source}: {count} times")
    
    def print_performance_metrics(self):
        """Display performance metrics"""
        print("\n⚡ PERFORMANCE METRICS")
        print("-" * 50)
        
        completed = kb_tracker.completed_actions
        if not completed:
            print("   No completed actions to analyze")
            return
        
        # Calculate success rate
        successful = len([a for a in completed if a.success])
        success_rate = (successful / len(completed)) * 100
        
        # Calculate average duration
        durations = []
        for action in completed:
            if action.execution_end:
                duration = self._parse_duration(action.execution_start, action.execution_end)
                if duration:
                    durations.append(duration)
        
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        # Count by action type
        action_types = {}
        for action in completed:
            action_types[action.action_type] = action_types.get(action.action_type, 0) + 1
        
        print(f"   📊 Success rate: {success_rate:.1f}% ({successful}/{len(completed)})")
        print(f"   ⏱️ Average duration: {avg_duration:.2f} seconds")
        print(f"   📈 Total actions: {len(completed)}")
        
        print("\n   Action type breakdown:")
        for action_type, count in sorted(action_types.items(), key=lambda x: x[1], reverse=True):
            print(f"      🎯 {action_type}: {count}")
    
    def _calculate_duration(self, start_time: str, end_time: str = None) -> str:
        """Calculate duration between timestamps"""
        try:
            start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            if end_time:
                end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            else:
                end = datetime.now()
            
            duration = end - start
            return f"{duration.total_seconds():.1f}s"
        except:
            return "unknown"
    
    def _parse_duration(self, start_time: str, end_time: str) -> float:
        """Parse duration in seconds"""
        try:
            start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            return (end - start).total_seconds()
        except:
            return 0
    
    def display_full_dashboard(self):
        """Display complete dashboard"""
        self.print_header()
        self.print_active_actions()
        self.print_recent_actions()
        self.print_collection_stats()
        self.print_filter_analysis()
        self.print_performance_metrics()
        print("\n" + "="*80)
    
    def display_summary(self):
        """Display compact summary"""
        summary = kb_tracker.get_action_summary()
        
        print(f"\n📊 KB OPERATIONS SUMMARY")
        print(f"   Active: {summary['active_actions']}")
        print(f"   Completed: {summary['completed_actions']}")
        print(f"   Success rate: {summary['successful_actions']}/{summary['completed_actions']}")
        
        total_docs = sum(stats['total_documents'] for stats in summary['collection_stats'].values())
        print(f"   Total documents: {total_docs}")

# Global dashboard instance
dashboard = KBDashboard()

def show_dashboard():
    """Show the full dashboard"""
    dashboard.display_full_dashboard()

def show_summary():
    """Show compact summary"""
    dashboard.display_summary()

if __name__ == "__main__":
    # Demo mode
    show_dashboard()
