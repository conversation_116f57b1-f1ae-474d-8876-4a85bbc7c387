#!/usr/bin/env python3
"""
Comprehensive test to validate all pipeline fixes and ensure perfection.
Tests logging, KB operations, document matching, and action execution.
"""

import os
import sys
import json
import logging
import time
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_logging_configuration():
    """Test that logging is properly configured without duplicates"""
    print("🧪 Testing logging configuration...")
    
    # Clear any existing handlers
    root_logger = logging.getLogger()
    initial_handler_count = len(root_logger.handlers)
    
    # Import manual_test to trigger logging setup
    try:
        from manual_test import logger
        
        # Check handler count after import
        final_handler_count = len(root_logger.handlers)
        
        if final_handler_count <= initial_handler_count + 2:  # Console + File handler
            print("✅ Logging configuration: CLEAN (no duplicates)")
            return True
        else:
            print(f"❌ Logging configuration: DUPLICATE HANDLERS ({final_handler_count} handlers)")
            return False
            
    except Exception as e:
        print(f"❌ Logging configuration test failed: {e}")
        return False

def test_kb_document_id_generation():
    """Test that KB operations generate valid document IDs"""
    print("🧪 Testing KB document ID generation...")
    
    try:
        from utils.knowledge_base_update_executor import KnowledgeBaseUpdateExecutor
        
        executor = KnowledgeBaseUpdateExecutor()
        
        # Test case 1: Action with valid target_document
        action1 = {
            'action_type': 'ADD_DOCUMENT',
            'target_document': 'RBI/FED/2024-25/17',
            'collection_name': ['rbi_circular'],
            'payload': {'content': 'Test content'}
        }
        
        notification_data = {
            'Title': 'Test Notification',
            'notification_codes': {'full_code': 'RBI/FED/2024-25/17'}
        }
        
        # This should not result in None document_id
        result1 = executor._add(action1, notification_data)
        
        if result1.get('success') and result1.get('document_id') != 'None':
            print("✅ Document ID generation: VALID IDs generated")
            return True
        else:
            print(f"❌ Document ID generation: Invalid result {result1}")
            return False
            
    except Exception as e:
        print(f"❌ KB document ID test failed: {e}")
        return False

def test_removal_action_execution():
    """Test that removal actions are properly executed"""
    print("🧪 Testing removal action execution...")
    
    try:
        from utils.knowledge_base_update_executor import KnowledgeBaseUpdateExecutor
        
        executor = KnowledgeBaseUpdateExecutor()
        
        # Test removal action
        removal_action = {
            'action_type': 'REMOVE_DOCUMENT',
            'target_document': 'RBI/FED/2023-24/15',
            'collection_name': ['rbi_circular'],
            'document_id': 'RBI/FED/2023-24/15'
        }
        
        notification_data = {
            'Title': 'Test Removal Notification'
        }
        
        # This should attempt removal (may not find documents, but should not be rejected)
        result = executor._remove(removal_action, notification_data)
        
        if result.get('action') == 'REMOVE_DOCUMENT':
            print("✅ Removal action execution: PROCESSED")
            return True
        else:
            print(f"❌ Removal action execution: Not processed {result}")
            return False
            
    except Exception as e:
        print(f"❌ Removal action test failed: {e}")
        return False

def test_filter_construction():
    """Test that filters are properly constructed"""
    print("🧪 Testing filter construction...")
    
    try:
        from utils.knowledge_base_update_executor import KnowledgeBaseUpdateExecutor
        
        executor = KnowledgeBaseUpdateExecutor()
        
        # Test filter construction
        action = {
            'action_type': 'REMOVE_DOCUMENT',
            'target_document': 'RBI/FED/2024-25/17',
            'document_id': 'RBI/FED/2024-25/17'
        }
        
        filter_obj = executor._build_filter(action)
        
        if filter_obj is not None:
            print("✅ Filter construction: SUCCESSFUL")
            return True
        else:
            print("❌ Filter construction: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Filter construction test failed: {e}")
        return False

def test_notification_processing():
    """Test basic notification processing"""
    print("🧪 Testing notification processing...")
    
    try:
        from manual_test import RealPipelineTest
        
        test_instance = RealPipelineTest()
        
        # Simple test notification
        test_notification = {
            'Title': 'Test Notification for Pipeline Validation',
            'Description': 'This is a test notification to validate the pipeline.',
            'Link': 'https://test.example.com',
            'Date': '2024-01-15'
        }
        
        # Process notification (should not crash)
        result = test_instance.process_notification_enhanced(test_notification)
        
        if result and 'status' in result:
            print("✅ Notification processing: FUNCTIONAL")
            return True
        else:
            print("❌ Notification processing: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Notification processing test failed: {e}")
        return False

def test_performance_metrics():
    """Test pipeline performance"""
    print("🧪 Testing pipeline performance...")
    
    try:
        # Test logging performance
        start_time = time.time()
        
        # Import and create logger
        from manual_test import logger
        
        # Log some messages
        for i in range(100):
            logger.info(f"Performance test message {i}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        if duration < 5.0:  # Should complete in under 5 seconds
            print(f"✅ Performance: GOOD ({duration:.2f}s for 100 log messages)")
            return True
        else:
            print(f"❌ Performance: SLOW ({duration:.2f}s for 100 log messages)")
            return False
            
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🚀 Starting comprehensive pipeline testing...")
    print("=" * 60)
    
    tests = [
        ("Logging Configuration", test_logging_configuration),
        ("KB Document ID Generation", test_kb_document_id_generation),
        ("Removal Action Execution", test_removal_action_execution),
        ("Filter Construction", test_filter_construction),
        ("Notification Processing", test_notification_processing),
        ("Performance Metrics", test_performance_metrics),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ CRASHED: {test_name} - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({(passed/total*100):.1f}%)")
    
    if passed == total:
        print("🎉 PIPELINE IS PERFECT! All systems operational.")
    elif passed >= total * 0.8:
        print("⚡ PIPELINE IS GOOD! Minor issues detected.")
    else:
        print("⚠️ PIPELINE NEEDS WORK! Major issues detected.")
    
    return passed == total

if __name__ == "__main__":
    # Set environment variable for testing
    os.environ['OPENAI_API_KEYS'] = os.getenv('OPENAI_API_KEYS', 'test-key')
    
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
