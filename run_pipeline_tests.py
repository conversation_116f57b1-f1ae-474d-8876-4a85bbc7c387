#!/usr/bin/env python3
"""
Pipeline Test Runner

This script runs all pipeline tests through manual_test.py and stores outputs
in a structured way for manual verification of KB actions.
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import traceback

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from manual_test import RealPipelineTest

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'pipeline_test_run_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PipelineTestRunner:
    """Run pipeline tests and store outputs for manual verification"""
    
    def __init__(self):
        self.pipeline = None
        self.test_results = {}
        self.output_dir = Path("test_outputs")
        self.output_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for organized output
        (self.output_dir / "individual_tests").mkdir(exist_ok=True)
        (self.output_dir / "kb_actions").mkdir(exist_ok=True)
        (self.output_dir / "summaries").mkdir(exist_ok=True)
        
    def initialize_pipeline(self):
        """Initialize the pipeline test instance"""
        try:
            logger.info("🚀 Initializing pipeline...")
            self.pipeline = RealPipelineTest()
            logger.info("✅ Pipeline initialized successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize pipeline: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def load_test_file(self, test_file_path: Path) -> List[Dict]:
        """Load test data from JSON file"""
        try:
            with open(test_file_path, 'r', encoding='utf-8') as f:
                test_data = json.load(f)
            logger.info(f"📁 Loaded {len(test_data)} test cases from {test_file_path.name}")
            return test_data
        except Exception as e:
            logger.error(f"❌ Failed to load test file {test_file_path}: {e}")
            return []
    
    def process_single_notification(self, notification: Dict, test_category: str) -> Dict:
        """Process a single notification through the pipeline"""
        try:
            notification_id = notification.get('notification_id', 'unknown')
            title = notification.get('title', 'Unknown Title')
            pdf_link = notification.get('pdf_link', '')
            
            logger.info(f"🔄 Processing notification {notification_id}: {title[:50]}...")
            
            # Create notification data structure expected by pipeline
            notification_data = {
                'Title': title,
                'PDF Link': pdf_link,
                'Link': pdf_link,  # Use PDF link as notification link
                'Date': datetime.now().strftime('%Y-%m-%d'),
                'notification_id': notification_id,
                'test_category': test_category,
                'expected_action': notification.get('action', ''),
                'summary': notification.get('summary', '')
            }
            
            # Step 1: Analyze notification
            logger.info(f"   📊 Step 1: Analyzing notification...")
            analysis_result = self.pipeline.notification_processor.analyze_notification(
                title=title,
                rss_description=notification.get('summary', ''),
                link=pdf_link
            )
            
            # Step 2: Download and extract PDF content
            logger.info(f"   📄 Step 2: Downloading and extracting PDF...")
            try:
                pdf_chunks, notification_codes, extracted_title, s3_url = self.pipeline.download_and_extract_pdf_content(
                    pdf_link, 
                    max_chars=8000,
                    return_chunks=True
                )
                
                # Update notification data with extracted info
                notification_data.update({
                    'extracted_title': extracted_title,
                    's3_url': s3_url,
                    'notification_codes': notification_codes,
                    'total_chunks': len(pdf_chunks)
                })
                
            except Exception as e:
                logger.error(f"   ❌ PDF processing failed: {e}")
                pdf_chunks = []
                notification_codes = {}
                s3_url = None
            
            # Step 3: Extract affected documents
            logger.info(f"   🔍 Step 3: Extracting affected documents...")
            affected_docs_result = self.pipeline.notification_processor.extract_affected_documents(
                title=title,
                content=notification.get('summary', ''),
                category=analysis_result.get('category', ''),
                notification_codes=notification_codes,
                chunks=pdf_chunks
            )
            
            # Step 4: Determine update actions
            logger.info(f"   🎯 Step 4: Determining update actions...")
            update_actions_result = self.pipeline.notification_processor.determine_update_actions(
                title=title,
                category=analysis_result.get('category', ''),
                affected_documents=affected_docs_result.get('document_actions', []),
                rss_description=notification.get('summary', ''),
                notification_data=notification_data
            )
            
            # Compile complete result
            result = {
                'notification_data': notification_data,
                'analysis_result': analysis_result,
                'affected_documents': affected_docs_result,
                'update_actions': update_actions_result,
                'processing_timestamp': datetime.now().isoformat(),
                'status': 'success'
            }
            
            logger.info(f"   ✅ Successfully processed notification {notification_id}")
            return result
            
        except Exception as e:
            logger.error(f"   ❌ Error processing notification {notification_id}: {e}")
            logger.error(traceback.format_exc())
            return {
                'notification_data': notification_data if 'notification_data' in locals() else notification,
                'error': str(e),
                'error_traceback': traceback.format_exc(),
                'processing_timestamp': datetime.now().isoformat(),
                'status': 'error'
            }
    
    def extract_kb_actions(self, result: Dict) -> List[Dict]:
        """Extract KB actions from processing result for manual verification"""
        kb_actions = []
        
        try:
            # Extract actions from update_actions_result
            update_actions = result.get('update_actions', {}).get('actions', [])
            for action in update_actions:
                kb_actions.append({
                    'source': 'update_actions',
                    'action_type': action.get('action_type', ''),
                    'target_document': action.get('target_document', ''),
                    'details': action.get('details', ''),
                    'priority': action.get('priority', ''),
                    'new_document_url': action.get('new_document_url', ''),
                    'rbi_page_url': action.get('rbi_page_url', ''),
                    'collection': action.get('collection', ''),
                    'reasoning': action.get('reasoning', '')
                })
            
            # Extract actions from affected_documents
            doc_actions = result.get('affected_documents', {}).get('document_actions', [])
            for action in doc_actions:
                kb_actions.append({
                    'source': 'affected_documents',
                    'action_type': action.get('action_required', ''),
                    'target_document': action.get('target_document', ''),
                    'document_type': action.get('document_type', ''),
                    'type_of_impact': action.get('type_of_impact', ''),
                    'collection': action.get('collection', ''),
                    'reasoning': action.get('reasoning', ''),
                    'new_document_url': action.get('new_document_url', ''),
                    'rbi_page_url': action.get('rbi_page_url', '')
                })
            
        except Exception as e:
            logger.error(f"Error extracting KB actions: {e}")
        
        return kb_actions
    
    def run_test_category(self, test_file_path: Path) -> Dict:
        """Run all tests in a category"""
        test_category = test_file_path.stem
        logger.info(f"🧪 Running test category: {test_category}")
        
        # Load test data
        test_data = self.load_test_file(test_file_path)
        if not test_data:
            return {'status': 'failed', 'reason': 'No test data loaded'}
        
        category_results = {
            'test_category': test_category,
            'total_tests': len(test_data),
            'successful_tests': 0,
            'failed_tests': 0,
            'test_results': [],
            'kb_actions_summary': [],
            'processing_timestamp': datetime.now().isoformat()
        }
        
        # Process each notification in the category
        for i, notification in enumerate(test_data):
            logger.info(f"📋 Processing test {i+1}/{len(test_data)} in {test_category}")
            
            result = self.process_single_notification(notification, test_category)
            
            if result.get('status') == 'success':
                category_results['successful_tests'] += 1
            else:
                category_results['failed_tests'] += 1
            
            # Extract KB actions for manual verification
            kb_actions = self.extract_kb_actions(result)
            result['kb_actions'] = kb_actions
            category_results['kb_actions_summary'].extend(kb_actions)
            
            category_results['test_results'].append(result)
            
            # Save individual test result
            individual_file = self.output_dir / "individual_tests" / f"{test_category}_{i+1}_{notification.get('notification_id', 'unknown')}.json"
            with open(individual_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        
        # Save category summary
        category_file = self.output_dir / "summaries" / f"{test_category}_summary.json"
        with open(category_file, 'w', encoding='utf-8') as f:
            json.dump(category_results, f, indent=2, ensure_ascii=False, default=str)
        
        # Save KB actions for manual verification
        kb_actions_file = self.output_dir / "kb_actions" / f"{test_category}_kb_actions.json"
        with open(kb_actions_file, 'w', encoding='utf-8') as f:
            json.dump({
                'test_category': test_category,
                'total_kb_actions': len(category_results['kb_actions_summary']),
                'kb_actions': category_results['kb_actions_summary'],
                'processing_timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"✅ Completed {test_category}: {category_results['successful_tests']}/{category_results['total_tests']} successful")
        return category_results
    
    def run_all_tests(self):
        """Run all pipeline tests"""
        logger.info("🚀 Starting pipeline test run...")
        
        if not self.initialize_pipeline():
            logger.error("❌ Failed to initialize pipeline, aborting tests")
            return
        
        # Find all test files
        test_files_dir = Path("pipeline_tests")
        test_files = list(test_files_dir.glob("*.json"))
        
        if not test_files:
            logger.error("❌ No test files found in pipeline_tests directory")
            return
        
        logger.info(f"📁 Found {len(test_files)} test categories")
        
        overall_results = {
            'total_categories': len(test_files),
            'successful_categories': 0,
            'failed_categories': 0,
            'category_results': {},
            'overall_stats': {
                'total_tests': 0,
                'successful_tests': 0,
                'failed_tests': 0,
                'total_kb_actions': 0
            },
            'processing_timestamp': datetime.now().isoformat()
        }
        
        # Run each test category
        for test_file in test_files:
            try:
                logger.info(f"\n{'='*60}")
                category_result = self.run_test_category(test_file)
                
                if category_result.get('status') != 'failed':
                    overall_results['successful_categories'] += 1
                else:
                    overall_results['failed_categories'] += 1
                
                overall_results['category_results'][test_file.stem] = category_result
                
                # Update overall stats
                overall_results['overall_stats']['total_tests'] += category_result.get('total_tests', 0)
                overall_results['overall_stats']['successful_tests'] += category_result.get('successful_tests', 0)
                overall_results['overall_stats']['failed_tests'] += category_result.get('failed_tests', 0)
                overall_results['overall_stats']['total_kb_actions'] += len(category_result.get('kb_actions_summary', []))
                
            except Exception as e:
                logger.error(f"❌ Failed to run test category {test_file.stem}: {e}")
                overall_results['failed_categories'] += 1
                overall_results['category_results'][test_file.stem] = {
                    'status': 'failed',
                    'error': str(e),
                    'error_traceback': traceback.format_exc()
                }
        
        # Save overall results
        overall_file = self.output_dir / "overall_test_results.json"
        with open(overall_file, 'w', encoding='utf-8') as f:
            json.dump(overall_results, f, indent=2, ensure_ascii=False, default=str)
        
        # Generate verification report
        self.generate_verification_report(overall_results)
        
        logger.info(f"\n{'='*60}")
        logger.info("🎉 Pipeline test run completed!")
        logger.info(f"📊 Overall Stats:")
        logger.info(f"   - Categories: {overall_results['successful_categories']}/{overall_results['total_categories']} successful")
        logger.info(f"   - Tests: {overall_results['overall_stats']['successful_tests']}/{overall_results['overall_stats']['total_tests']} successful")
        logger.info(f"   - KB Actions: {overall_results['overall_stats']['total_kb_actions']} total")
        logger.info(f"📁 Results saved to: {self.output_dir}")
    
    def generate_verification_report(self, overall_results: Dict):
        """Generate a human-readable verification report"""
        report_file = self.output_dir / "VERIFICATION_REPORT.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Pipeline Test Verification Report\n\n")
            f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Overall summary
            f.write("## Overall Summary\n\n")
            stats = overall_results['overall_stats']
            f.write(f"- **Total Categories:** {overall_results['total_categories']}\n")
            f.write(f"- **Successful Categories:** {overall_results['successful_categories']}\n")
            f.write(f"- **Total Tests:** {stats['total_tests']}\n")
            f.write(f"- **Successful Tests:** {stats['successful_tests']}\n")
            f.write(f"- **Failed Tests:** {stats['failed_tests']}\n")
            f.write(f"- **Total KB Actions:** {stats['total_kb_actions']}\n\n")
            
            # Category breakdown
            f.write("## Category Breakdown\n\n")
            for category, result in overall_results['category_results'].items():
                if result.get('status') == 'failed':
                    f.write(f"### ❌ {category} (FAILED)\n")
                    f.write(f"**Error:** {result.get('error', 'Unknown error')}\n\n")
                else:
                    f.write(f"### ✅ {category}\n")
                    f.write(f"- **Tests:** {result.get('successful_tests', 0)}/{result.get('total_tests', 0)} successful\n")
                    f.write(f"- **KB Actions:** {len(result.get('kb_actions_summary', []))}\n\n")
            
            # Manual verification instructions
            f.write("## Manual Verification Instructions\n\n")
            f.write("1. **Review Individual Test Results:**\n")
            f.write("   - Check `test_outputs/individual_tests/` for detailed results\n")
            f.write("   - Verify that analysis results match expected outcomes\n\n")
            
            f.write("2. **Verify KB Actions:**\n")
            f.write("   - Check `test_outputs/kb_actions/` for extracted actions\n")
            f.write("   - Ensure actions are appropriate for each notification type\n")
            f.write("   - Verify target documents and collections are correct\n\n")
            
            f.write("3. **Validate Processing Logic:**\n")
            f.write("   - Review reasoning provided for each action\n")
            f.write("   - Check that supersession detection works correctly\n")
            f.write("   - Verify URL extraction and document identification\n\n")
            
            f.write("## Files for Review\n\n")
            f.write("- `overall_test_results.json` - Complete test results\n")
            f.write("- `summaries/` - Category summaries\n")
            f.write("- `individual_tests/` - Detailed individual test results\n")
            f.write("- `kb_actions/` - Extracted KB actions for verification\n")
        
        logger.info(f"📋 Verification report generated: {report_file}")

def main():
    """Main entry point"""
    runner = PipelineTestRunner()
    runner.run_all_tests()

if __name__ == "__main__":
    main()