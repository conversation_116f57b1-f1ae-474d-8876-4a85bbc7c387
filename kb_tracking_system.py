"""
Knowledge Base Change Tracking System
=====================================

This module provides comprehensive tracking for knowledge base operations,
including filter construction, collection targeting, and document retrieval.
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class FilterCondition:
    """Represents a single filter condition"""
    field: str
    value: Any
    condition_type: str  # 'exact', 'list', 'vector_search'
    source: str  # 'filter_fields', 'metadata_map', 'target_document'

@dataclass
class ActionTracker:
    """Tracks a single KB action execution"""
    action_id: str
    action_type: str
    target_document: Optional[str]
    collection: str
    filter_conditions: List[FilterCondition]
    execution_start: str
    execution_end: Optional[str] = None
    success: Optional[bool] = None
    error_message: Optional[str] = None
    documents_affected: int = 0
    vector_search_used: bool = False
    fuzzy_match_used: bool = False
    
    def to_dict(self):
        return asdict(self)

@dataclass
class CollectionStats:
    """Statistics for a collection"""
    collection_name: str
    total_documents: int
    documents_added: int
    documents_removed: int
    documents_updated: int
    last_operation: str

class KBChangeTracker:
    """
    Comprehensive tracking system for KB operations
    """
    
    def __init__(self, log_file: str = "kb_operations.log"):
        self.log_file = Path(log_file)
        self.active_actions: Dict[str, ActionTracker] = {}
        self.completed_actions: List[ActionTracker] = []
        self.collection_stats: Dict[str, CollectionStats] = {}
        
        # Initialize collection stats
        for collection in ['rbi_circular', 'rbi_master_circular', 'rbi_master_direction', 'rbi_other']:
            self.collection_stats[collection] = CollectionStats(
                collection_name=collection,
                total_documents=0,
                documents_added=0,
                documents_removed=0,
                documents_updated=0,
                last_operation="none"
            )
        
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup dedicated logging for KB tracking"""
        handler = logging.FileHandler(self.log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - KB_TRACKER - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def start_action_tracking(self, action: Dict[str, Any]) -> str:
        """Start tracking a KB action"""
        action_id = f"action_{int(time.time() * 1000)}"
        
        # Extract filter conditions
        filter_conditions = self._extract_filter_conditions(action)
        
        tracker = ActionTracker(
            action_id=action_id,
            action_type=action.get('action_type', 'UNKNOWN'),
            target_document=action.get('target_document'),
            collection=action.get('collection', 'unknown'),
            filter_conditions=filter_conditions,
            execution_start=datetime.now().isoformat(),
            vector_search_used=action.get('use_vector_search', False),
            fuzzy_match_used=action.get('use_fuzzy', False)
        )
        
        self.active_actions[action_id] = tracker
        
        self.logger.info(f"🎯 STARTED: {tracker.action_type} action {action_id}")
        self.logger.info(f"   Target: {tracker.target_document}")
        self.logger.info(f"   Collection: {tracker.collection}")
        self.logger.info(f"   Filter conditions: {len(filter_conditions)}")
        
        for condition in filter_conditions:
            self.logger.info(f"   📋 Filter: {condition.field} = {condition.value} ({condition.source})")
        
        return action_id
    
    def _extract_filter_conditions(self, action: Dict[str, Any]) -> List[FilterCondition]:
        """Extract filter conditions from action"""
        conditions = []
        
        # From filter_fields
        filter_fields = action.get('filter_fields', {})
        for field, value in filter_fields.items():
            condition_type = 'list' if isinstance(value, list) else 'exact'
            conditions.append(FilterCondition(
                field=field,
                value=value,
                condition_type=condition_type,
                source='filter_fields'
            ))
        
        # From metadata mapping (common fields)
        metadata_map = {
            'document_id': 'metadata.document_id',
            'short_id': 'metadata.short_id',
            'long_code': 'metadata.long_code',
            'document_title': 'metadata.title'
        }
        
        for key, path in metadata_map.items():
            if key in action and action[key]:
                conditions.append(FilterCondition(
                    field=path,
                    value=action[key],
                    condition_type='exact',
                    source='metadata_map'
                ))
        
        # From target_document fallback
        if action.get('target_document') and not any(c.source == 'metadata_map' for c in conditions):
            conditions.append(FilterCondition(
                field='metadata.document_id',
                value=action['target_document'],
                condition_type='exact',
                source='target_document'
            ))
        
        return conditions
    
    def complete_action_tracking(self, action_id: str, success: bool, 
                                documents_affected: int = 0, error_message: str = None):
        """Complete tracking for an action"""
        if action_id not in self.active_actions:
            self.logger.warning(f"⚠️ Action {action_id} not found in active tracking")
            return
        
        tracker = self.active_actions[action_id]
        tracker.execution_end = datetime.now().isoformat()
        tracker.success = success
        tracker.documents_affected = documents_affected
        tracker.error_message = error_message
        
        # Update collection stats
        if success and tracker.collection in self.collection_stats:
            stats = self.collection_stats[tracker.collection]
            if tracker.action_type == 'ADD_DOCUMENT':
                stats.documents_added += documents_affected
                stats.total_documents += documents_affected
            elif tracker.action_type == 'REMOVE_DOCUMENT':
                stats.documents_removed += documents_affected
                stats.total_documents -= documents_affected
            elif tracker.action_type == 'UPDATE_DOCUMENT':
                stats.documents_updated += documents_affected
            stats.last_operation = tracker.action_type
        
        # Move to completed
        self.completed_actions.append(tracker)
        del self.active_actions[action_id]
        
        status = "✅ SUCCESS" if success else "❌ FAILED"
        self.logger.info(f"{status}: {tracker.action_type} action {action_id}")
        self.logger.info(f"   Documents affected: {documents_affected}")
        if error_message:
            self.logger.error(f"   Error: {error_message}")
    
    def get_action_summary(self) -> Dict[str, Any]:
        """Get summary of all tracked actions"""
        return {
            'active_actions': len(self.active_actions),
            'completed_actions': len(self.completed_actions),
            'successful_actions': len([a for a in self.completed_actions if a.success]),
            'failed_actions': len([a for a in self.completed_actions if not a.success]),
            'collection_stats': {name: asdict(stats) for name, stats in self.collection_stats.items()}
        }
    
    def export_tracking_data(self, filename: str = None) -> str:
        """Export all tracking data to JSON file"""
        if not filename:
            filename = f"kb_tracking_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        data = {
            'export_timestamp': datetime.now().isoformat(),
            'active_actions': [tracker.to_dict() for tracker in self.active_actions.values()],
            'completed_actions': [tracker.to_dict() for tracker in self.completed_actions],
            'collection_stats': {name: asdict(stats) for name, stats in self.collection_stats.items()},
            'summary': self.get_action_summary()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 Exported tracking data to {filename}")
        return filename

# Global tracker instance
kb_tracker = KBChangeTracker()

def track_kb_action(action: Dict[str, Any]) -> str:
    """Convenience function to start tracking an action"""
    return kb_tracker.start_action_tracking(action)

def complete_kb_action(action_id: str, success: bool, documents_affected: int = 0, error_message: str = None):
    """Convenience function to complete tracking an action"""
    kb_tracker.complete_action_tracking(action_id, success, documents_affected, error_message)

def get_kb_summary() -> Dict[str, Any]:
    """Convenience function to get KB operation summary"""
    return kb_tracker.get_action_summary()
