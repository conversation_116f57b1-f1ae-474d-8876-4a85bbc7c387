"""
S3 utility functions for interacting with AWS S3
"""

import boto3
import logging
from pathlib import Path
from botocore.exceptions import Client<PERSON>rror
from typing import Optional, Union, Dict, Any
from .config import config

logger = logging.getLogger(__name__)

def get_s3_client():
    """
    Create and return an S3 client using the configured AWS credentials
    
    Returns:
        boto3.client: Configured S3 client
    """
    # Use actual values from config instead of printing mock objects
    access_key_id = "********************"
    secret_access_key = "huMB0J5q7s3ImyOqTH3G5FTKSBoBIqkWCnCBuqKm"
    region = "ap-south-1"
    
    print(f"Creating S3 client with access key: {access_key_id}, region: {region}")
    return boto3.client(
        's3',
        aws_access_key_id=access_key_id,
        aws_secret_access_key=secret_access_key,
        region_name=region
    )

def upload_file_to_s3(
    local_file_path: Union[str, Path], 
    s3_key: Optional[str] = None, 
    bucket_name: Optional[str] = None,
    metadata: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """
    Upload a file to S3 bucket
    
    Args:
        local_file_path: Path to the local file to upload
        s3_key: Key (path) to use in S3. If None, uses the filename
        bucket_name: S3 bucket name, defaults to configured bucket
        metadata: Optional metadata to attach to the S3 object
        
    Returns:
        Dict with upload status and S3 URL
    """
    try:
        print(f"Type of local_file_path: {type(local_file_path)}")
        # Convert to Path object and check if it exists
        if isinstance(local_file_path, str):
            local_file_path = Path(local_file_path)
            print(f"Converted local_file_path to Path: {local_file_path}")
        elif not isinstance(local_file_path, Path):
            local_file_path = Path(str(local_file_path))
        
        print(f"Final local_file_path type: {type(local_file_path)}, value: {local_file_path}")
        logger.debug(f"Checking if file exists: {local_file_path} (type: {type(local_file_path)})")
        
        if not local_file_path.exists():
            print(f"File not found: {local_file_path}")
            logger.error(f"File not found: {local_file_path}")
            return {"success": False, "error": "File not found", "s3_url": None}
    except Exception as e:
        logger.error(f"Error validating file path: {e}, path: {local_file_path}, type: {type(local_file_path)}")
        return {"success": False, "error": f"Invalid file path: {str(e)}", "s3_url": None}
        
    # Use defaults from config if not specified
    if bucket_name is None:
        bucket_name = "localdocdump"
        
    if s3_key is None:
        s3_key = local_file_path.name
        
    # Ensure s3_key doesn't start with / to avoid unexpected S3 paths
    s3_key = s3_key.lstrip('/')
    
    # Prepare metadata if provided
    extra_args = {
        'ContentType': 'application/pdf',
        'ContentDisposition': 'inline',
    }
    if metadata:
        # Ensure all metadata values are strings as required by S3 API
        string_metadata = {}
        for key, value in metadata.items():
            string_metadata[key] = str(value)
        
        logger.debug(f"S3 metadata (converted to strings): {string_metadata}")
        extra_args['Metadata'] = string_metadata
        
    try:
        s3_client = get_s3_client()
        print(f"Using S3 client: {s3_client}")
        # Convert to string to ensure compatibility
        file_path_str = str(local_file_path)
        print(f"Uploading file to S3: {file_path_str}, bucket: {bucket_name}, key: {s3_key}")
        logger.debug(f"Uploading file: path={file_path_str}, bucket={bucket_name}, key={s3_key}")
        print(f"Extra args for upload: {extra_args}")
        s3_client.upload_file(
            file_path_str,
            bucket_name,
            s3_key,
            ExtraArgs=extra_args
        )
        
        # Generate S3 URL
        region_name = 'ap-south-1'  # Adjust as needed
        s3_url = f"https://{bucket_name}.s3.{region_name}.amazonaws.com/{s3_key}"
        logger.info(f"Successfully uploaded file to {s3_url}")
        print(f"Upload successful! S3 URL: {s3_url}")
        
        return {
            "success": True, 
            "s3_url": s3_url,
            "bucket": bucket_name,
            "key": s3_key
        }
        
    except ClientError as e:
        logger.error(f"S3 upload error (ClientError): {e}")
        return {"success": False, "error": str(e), "s3_url": None}
    except TypeError as e:
        logger.error(f"S3 upload error (TypeError): {e}, type(local_file_path)={type(local_file_path)}, bucket_name={bucket_name}, type(s3_key)={type(s3_key)}")
        return {"success": False, "error": f"TypeError: {str(e)}", "s3_url": None}
    except Exception as e:
        logger.error(f"Unexpected S3 upload error: {e}")
        return {"success": False, "error": f"Unexpected error: {str(e)}", "s3_url": None}
        
def download_file_from_s3(
    s3_key: str, 
    local_file_path: Union[str, Path],
    bucket_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Download a file from S3 bucket
    
    Args:
        s3_key: Key (path) in S3
        local_file_path: Path where to save the file locally
        bucket_name: S3 bucket name, defaults to configured bucket
        
    Returns:
        Dict with download status
    """
    local_file_path = Path(local_file_path)
    
    # Create directory if it doesn't exist
    local_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Use default from config if not specified
    if bucket_name is None:
        bucket_name = "localdocdump"
    try:
        s3_client = get_s3_client()
        s3_client.download_file(bucket_name, s3_key, str(local_file_path))
        
        logger.info(f"Successfully downloaded s3://{bucket_name}/{s3_key} to {local_file_path}")
        return {"success": True}
        
    except ClientError as e:
        logger.error(f"S3 download error: {e}")
        return {"success": False, "error": str(e)}

def get_s3_url(key: str, bucket_name: Optional[str] = None) -> str:
    """
    Generate S3 URL for a given key
    
    Args:
        key: S3 object key
        bucket_name: S3 bucket name, defaults to configured bucket
        
    Returns:
        S3 URL in the format https://{bucket}.s3.{region}.amazonaws.com/{key}
    """
    if bucket_name is None:
        bucket_name = "localdocdump"
        
    region_name = 'ap-south-1'
    return f"https://{bucket_name}.s3.{region_name}.amazonaws.com/{key.lstrip('/')}"

# Simple usage function
def upload_and_get_link(file_path: str) -> str:
    """
    Simple function to upload file and get S3 link
    
    Args:
        file_path: Path to file to upload
        
    Returns:
        S3 URL if successful, None if failed
    """
    result = upload_file_to_s3(file_path)
    if result["success"]:
        return result["s3_url"]
    else:
        print(f"Upload failed: {result['error']}")
        return None