"""
Enhanced Document ID Extraction and Validation System

This module provides robust document ID extraction, normalization, and validation
to reduce brittle extraction issues that cause mis-routing in the pipeline.

Key improvements:
1. Comprehensive regex patterns for all RBI document types
2. Robust normalization and cleaning
3. Confidence scoring for extractions
4. Fallback mechanisms for edge cases
5. Integration with manual review system
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class DocumentIDType(str, Enum):
    """Document ID types for classification"""
    RBI_GENERAL = "rbi_general"           # RBI/FED/2024-25/17
    RBI_DEPARTMENT = "rbi_department"     # RBI/DOR/2024-25/80
    CIRCULAR_LONG = "circular_long"       # FIDD.CO.Plan.BC.No.4/04.09.01/2024-25
    CIRCULAR_SHORT = "circular_short"     # Circular No. 123
    MASTER_DIRECTION = "master_direction" # Master Direction No. XYZ
    AP_SERIES = "ap_series"              # A.P.(DIR Series) Circular No. 45
    DBR_SERIES = "dbr_series"            # DBR.No.Ret.BC.78/12.02.001/2024-25
    FEMA_SERIES = "fema_series"          # FEMA 20(R)/2024-FED
    UNKNOWN = "unknown"

@dataclass
class DocumentIDExtraction:
    """Result of document ID extraction"""
    document_id: str
    confidence: float  # 0.0 to 1.0
    id_type: DocumentIDType
    source: str  # 'title', 'content', 'pdf_link', 'fallback'
    normalized_id: str
    requires_manual_review: bool = False
    extraction_notes: str = ""

class EnhancedDocumentExtractor:
    """Enhanced document ID extractor with robust patterns and validation"""
    
    def __init__(self):
        self.patterns = self._initialize_patterns()
        self.confidence_weights = {
            'exact_match': 1.0,
            'partial_match': 0.8,
            'fuzzy_match': 0.6,
            'fallback': 0.3
        }
    
    def _initialize_patterns(self) -> Dict[DocumentIDType, List[Dict[str, Any]]]:
        """Initialize comprehensive regex patterns for all document types"""
        return {
            DocumentIDType.RBI_GENERAL: [
                {
                    'pattern': r'RBI/([A-Z]{2,5})/(\d{4}-\d{2})/(\d+)',
                    'confidence': 0.95,
                    'description': 'Standard RBI format with department'
                },
                {
                    'pattern': r'RBI/(\d{4}-\d{2})/(\d+)',
                    'confidence': 0.90,
                    'description': 'RBI format without department'
                },
                {
                    'pattern': r'RBI[/\\]([A-Z]{2,5})[/\\](\d{4})[/\\](\d+)',
                    'confidence': 0.85,
                    'description': 'RBI format with year only'
                }
            ],
            
            DocumentIDType.CIRCULAR_LONG: [
                {
                    'pattern': r'([A-Z]{2,5}\.(?:[A-Z]{2,5}\.)*[A-Z]{2,5}\.(?:No\.)?[A-Za-z0-9\-]+/\d+(?:\.\d+)*\.\d+/\d{4}(?:-\d{2})?)',
                    'confidence': 0.95,
                    'description': 'Long format circular codes'
                },
                {
                    'pattern': r'([A-Z]{2,5}\.[A-Z]{2,5}\.[A-Z]{2,5}\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2})',
                    'confidence': 0.90,
                    'description': 'Standard long circular format'
                }
            ],
            
            DocumentIDType.DBR_SERIES: [
                {
                    'pattern': r'(DBR\.No\.([A-Z]{2,5}\.)*[A-Z]{2,5}\.BC\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2})',
                    'confidence': 0.95,
                    'description': 'DBR series with BC designation'
                },
                {
                    'pattern': r'(DBR\.No\.[A-Za-z\.]+\d+/[\d\.]+/\d{4}-\d{2})',
                    'confidence': 0.85,
                    'description': 'General DBR format'
                }
            ],
            
            DocumentIDType.AP_SERIES: [
                {
                    'pattern': r'A\.P\.\s*\(DIR\s*Series\)\s*Circular\s*No\.\s*(\d+)',
                    'confidence': 0.95,
                    'description': 'A.P. DIR Series circular'
                }
            ],
            
            DocumentIDType.FEMA_SERIES: [
                {
                    'pattern': r'FEMA\s*(\d+)\s*\([A-Z]*\)/(\d+)/(\d{4})-([A-Z]+)',
                    'confidence': 0.95,
                    'description': 'FEMA notification format'
                }
            ],
            
            DocumentIDType.CIRCULAR_SHORT: [
                {
                    'pattern': r'Circular\s+No\.\s*([A-Za-z0-9\.\-/]+)',
                    'confidence': 0.80,
                    'description': 'Short circular reference'
                }
            ],
            
            DocumentIDType.MASTER_DIRECTION: [
                {
                    'pattern': r'Master\s+Direction\s+No\.\s*([A-Za-z0-9\.\-/]+)',
                    'confidence': 0.90,
                    'description': 'Master Direction reference'
                }
            ]
        }
    
    def extract_document_id(self, title: str = "", content: str = "", 
                          pdf_link: str = "", notification_codes: Dict = None) -> DocumentIDExtraction:
        """
        Extract document ID with comprehensive pattern matching and confidence scoring
        
        Args:
            title: Notification title
            content: Notification content
            pdf_link: PDF link URL
            notification_codes: Pre-extracted notification codes
            
        Returns:
            DocumentIDExtraction with best match and confidence score
        """
        extractions = []
        
        # Try notification codes first (highest confidence)
        if notification_codes:
            extraction = self._extract_from_codes(notification_codes)
            if extraction:
                extractions.append(extraction)
        
        # Try title extraction
        if title:
            extraction = self._extract_from_text(title, "title")
            if extraction:
                extractions.append(extraction)
        
        # Try content extraction
        if content:
            extraction = self._extract_from_text(content[:2000], "content")  # Limit content size
            if extraction:
                extractions.append(extraction)
        
        # Try PDF link extraction
        if pdf_link:
            extraction = self._extract_from_pdf_link(pdf_link)
            if extraction:
                extractions.append(extraction)
        
        # Select best extraction based on confidence
        if extractions:
            best_extraction = max(extractions, key=lambda x: x.confidence)
            
            # Apply additional validation
            best_extraction = self._validate_and_enhance(best_extraction)
            
            return best_extraction
        
        # Fallback extraction
        return self._create_fallback_extraction(title, content, pdf_link)
    
    def _extract_from_codes(self, notification_codes: Dict) -> Optional[DocumentIDExtraction]:
        """Extract from pre-parsed notification codes"""
        try:
            # Priority order for code selection
            code_priorities = ['long_code', 'short_code', 'full_code']
            
            for code_key in code_priorities:
                code = notification_codes.get(code_key, '').strip()
                if code and len(code) > 5:  # Basic validation
                    normalized = self._normalize_document_id(code)
                    id_type = self._classify_document_id(normalized)
                    
                    return DocumentIDExtraction(
                        document_id=code,
                        confidence=0.95,  # High confidence for pre-parsed codes
                        id_type=id_type,
                        source='notification_codes',
                        normalized_id=normalized,
                        extraction_notes=f"Extracted from {code_key}"
                    )
            
            return None
            
        except Exception as e:
            logger.warning(f"Error extracting from notification codes: {e}")
            return None
    
    def _extract_from_text(self, text: str, source: str) -> Optional[DocumentIDExtraction]:
        """Extract document ID from text using pattern matching"""
        if not text:
            return None
        
        best_match = None
        best_confidence = 0.0
        
        # Try each pattern type
        for id_type, pattern_list in self.patterns.items():
            for pattern_info in pattern_list:
                pattern = pattern_info['pattern']
                base_confidence = pattern_info['confidence']
                
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    extracted_id = match.group(0).strip()
                    
                    # Calculate confidence based on context
                    confidence = self._calculate_confidence(extracted_id, text, source, base_confidence)
                    
                    if confidence > best_confidence:
                        normalized = self._normalize_document_id(extracted_id)
                        
                        best_match = DocumentIDExtraction(
                            document_id=extracted_id,
                            confidence=confidence,
                            id_type=id_type,
                            source=source,
                            normalized_id=normalized,
                            extraction_notes=pattern_info['description']
                        )
                        best_confidence = confidence
        
        return best_match

    def _extract_from_pdf_link(self, pdf_link: str) -> Optional[DocumentIDExtraction]:
        """Extract document ID from PDF link"""
        try:
            # Extract filename from PDF link
            pdf_match = re.search(r'PDFs/(.+?)\.PDF', pdf_link, re.IGNORECASE)
            if pdf_match:
                pdf_id = pdf_match.group(1)

                # Try to extract meaningful ID from filename
                # Clean up the PDF ID but preserve structure
                clean_id = re.sub(r'[^A-Za-z0-9_\-\./]', '_', pdf_id)

                # Try to find document patterns in the filename
                for id_type, pattern_list in self.patterns.items():
                    for pattern_info in pattern_list:
                        pattern = pattern_info['pattern']
                        match = re.search(pattern, clean_id, re.IGNORECASE)
                        if match:
                            extracted_id = match.group(0)
                            normalized = self._normalize_document_id(extracted_id)

                            return DocumentIDExtraction(
                                document_id=extracted_id,
                                confidence=0.70,  # Medium confidence for PDF link extraction
                                id_type=id_type,
                                source='pdf_link',
                                normalized_id=normalized,
                                extraction_notes=f"Extracted from PDF filename: {pdf_id}"
                            )

                # Fallback: use cleaned filename
                if len(clean_id) > 5:
                    normalized = self._normalize_document_id(clean_id[:50])  # Limit length
                    return DocumentIDExtraction(
                        document_id=clean_id[:50],
                        confidence=0.40,
                        id_type=DocumentIDType.UNKNOWN,
                        source='pdf_link',
                        normalized_id=normalized,
                        requires_manual_review=True,
                        extraction_notes=f"Fallback from PDF filename: {pdf_id}"
                    )

            return None

        except Exception as e:
            logger.warning(f"Error extracting from PDF link: {e}")
            return None

    def _calculate_confidence(self, extracted_id: str, text: str, source: str, base_confidence: float) -> float:
        """Calculate confidence score based on context and validation"""
        confidence = base_confidence

        # Boost confidence for certain sources
        if source == 'title':
            confidence *= 1.1
        elif source == 'notification_codes':
            confidence *= 1.2

        # Boost confidence for longer, more specific IDs
        if len(extracted_id) > 20:
            confidence *= 1.05
        elif len(extracted_id) < 10:
            confidence *= 0.9

        # Check for validation patterns
        if self._has_valid_year_pattern(extracted_id):
            confidence *= 1.05

        if self._has_valid_department_pattern(extracted_id):
            confidence *= 1.05

        # Penalize if ID appears in suspicious context
        if 'example' in text.lower() or 'sample' in text.lower():
            confidence *= 0.7

        # Ensure confidence stays within bounds
        return min(confidence, 1.0)

    def _has_valid_year_pattern(self, document_id: str) -> bool:
        """Check if document ID has valid year pattern"""
        year_pattern = r'20\d{2}(?:-\d{2})?'
        return bool(re.search(year_pattern, document_id))

    def _has_valid_department_pattern(self, document_id: str) -> bool:
        """Check if document ID has valid department pattern"""
        dept_patterns = [
            r'RBI/[A-Z]{2,5}/',
            r'[A-Z]{2,5}\.[A-Z]{2,5}\.',
            r'DBR\.No\.',
            r'FEMA'
        ]
        return any(re.search(pattern, document_id, re.IGNORECASE) for pattern in dept_patterns)

    def _classify_document_id(self, document_id: str) -> DocumentIDType:
        """Classify document ID type based on pattern"""
        if not document_id:
            return DocumentIDType.UNKNOWN

        # Check each pattern type
        for id_type, pattern_list in self.patterns.items():
            for pattern_info in pattern_list:
                if re.search(pattern_info['pattern'], document_id, re.IGNORECASE):
                    return id_type

        return DocumentIDType.UNKNOWN

    def _normalize_document_id(self, document_id: str) -> str:
        """Normalize document ID for consistency"""
        if not document_id:
            return document_id

        # Import existing normalization function
        try:
            from improved_removal_handler import normalize_document_id
            return normalize_document_id(document_id)
        except ImportError:
            # Fallback normalization
            normalized = document_id.strip()
            normalized = re.sub(r'\s+', ' ', normalized)  # Normalize whitespace
            normalized = normalized.replace('\\', '/')     # Normalize slashes
            return normalized

    def _validate_and_enhance(self, extraction: DocumentIDExtraction) -> DocumentIDExtraction:
        """Validate and enhance extraction with additional checks"""
        # Check if requires manual review based on confidence
        if extraction.confidence < 0.6:
            extraction.requires_manual_review = True
            extraction.extraction_notes += " [Low confidence - manual review recommended]"

        # Check for special cases that should be flagged
        special_indicators = ['MANUAL_REVIEW_REQUIRED', 'TEST_', 'EXAMPLE_', 'SAMPLE_']
        if any(indicator in extraction.document_id.upper() for indicator in special_indicators):
            extraction.requires_manual_review = True
            extraction.extraction_notes += " [Special case detected]"

        # Validate document ID format
        if len(extraction.document_id) < 5:
            extraction.requires_manual_review = True
            extraction.extraction_notes += " [ID too short]"

        return extraction

    def _create_fallback_extraction(self, title: str, content: str, pdf_link: str) -> DocumentIDExtraction:
        """Create fallback extraction when no patterns match"""
        # Try to create a meaningful fallback ID
        fallback_id = "MANUAL_REVIEW_REQUIRED"
        source = "fallback"
        notes = "No document ID patterns matched"

        # Try to extract something meaningful from title
        if title:
            # Look for any numeric patterns that might be document references
            numeric_patterns = re.findall(r'\d{4}[-/]\d{2}[-/]\d+|\d{4}[-/]\d+|No\.\s*\d+', title)
            if numeric_patterns:
                fallback_id = f"EXTRACTED_{numeric_patterns[0].replace(' ', '_')}"
                notes = f"Fallback extraction from title: {numeric_patterns[0]}"

        return DocumentIDExtraction(
            document_id=fallback_id,
            confidence=0.1,
            id_type=DocumentIDType.UNKNOWN,
            source=source,
            normalized_id=self._normalize_document_id(fallback_id),
            requires_manual_review=True,
            extraction_notes=notes
        )
