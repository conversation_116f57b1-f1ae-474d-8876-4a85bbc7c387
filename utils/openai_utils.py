"""
Enhanced OpenAI utilities with improved key management and error handling
"""

import time
import random
import logging
from typing import List, Dict, Any, Optional, Callable
from openai import OpenAI, RateLimitError
from langchain_openai import OpenAIEmbeddings
from bs4 import BeautifulSoup
import html2text

from dataclasses import dataclass
import itertools
from utils.config import config

@dataclass
class OpenAIConfig:
    """OpenAI configuration settings"""
    api_keys: List[str]
    embedding_model: str = "text-embedding-3-large"
    chat_model: str = "gpt-4.1-mini-2025-04-14"
    nano_model: str = "gpt-4.1-nano-2025-04-14"
    max_retries: int = 5
    retry_min_seconds: int = 2
    retry_max_seconds: int = 4
    
    def __post_init__(self):
        self.key_cycle = itertools.cycle(self.api_keys)
    
    def get_next_key(self) -> str:
        """Get the next API key in rotation"""
        return next(self.key_cycle)
    
logger = logging.getLogger(__name__)

class OpenAIManager:
    """Manages OpenAI client with key rotation and error handling"""

    def __init__(self):
        from utils.config import config
        self.config = config.openai
        self.client = OpenAI(api_key=self.config.get_next_key())
        self._current_key_index = 0

    def with_key_rotation(self, callable_fn: Callable, *args, **kwargs) -> Any:
        """
        Execute OpenAI API call with automatic key rotation on rate limits
        """
        tried_keys = set()
        total_keys = len(self.config.api_keys)

        for attempt in range(total_keys):
            current_key = self.client.api_key

            try:
                return callable_fn(*args, **kwargs)

            except RateLimitError as e:
                # Log the specific error type
                error_code = None
                try:
                    if hasattr(e, 'response') and e.response:
                        error_data = e.response.json().get("error", {})
                        error_code = error_data.get("code")
                except Exception:
                    pass

                if error_code == "insufficient_quota":
                    logger.error(f"Key {current_key[:6]}... out of quota. Rotating to next key.")
                else:
                    logger.warning(f"Rate limit hit on key {current_key[:6]}... Rotating to next key.")

                tried_keys.add(current_key)

                # Get next key and update client
                next_key = self.config.get_next_key()
                self.client.api_key = next_key

                # Add small delay before retry
                time.sleep(random.uniform(1, 3))
                continue

            except Exception as e:
                logger.error(f"Unexpected error with OpenAI API: {e}")
                raise

        # If we get here, all keys failed
        raise RuntimeError(
            f"All {total_keys} OpenAI API keys failed with RateLimitError or insufficient_quota."
        )

    def get_client(self) -> OpenAI:
        """Get the current OpenAI client"""
        return self.client

    def get_completion(self, prompt: str, model: str = "gpt-4.1-mini-2025-04-14", temperature: float = 0.1, response_format=None) -> str:
        """Get a simple text completion from OpenAI with key rotation"""
        try:
            kwargs = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": temperature
            }
            
            # Only add response_format if it's provided
            if response_format:
                kwargs["response_format"] = response_format
                
            response = self.with_key_rotation(
                lambda: self.client.chat.completions.create(**kwargs)
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Completion call failed: {e}")
            raise

# Global OpenAI manager instance
openai_manager = OpenAIManager()

# Global client instance
client_openai = openai_manager.get_client()


def strip_data_attributes(html: str) -> str:
    """
    Remove all data-* attributes from HTML content and convert to clean text
    """
    soup = BeautifulSoup(html, "html.parser")
    for tag in soup.find_all():
        attrs_to_remove = [attr for attr in tag.attrs if attr.startswith("data-")]
        for attr in attrs_to_remove:
            del tag.attrs[attr]

    cleaned_html = str(soup)
    return html2text.html2text(cleaned_html)

class RateLimitedOpenAIEmbeddings(OpenAIEmbeddings):
    """
    Enhanced OpenAI embeddings with rate limiting and key rotation
    """

    def __init__(self, **kwargs):
        # Use the first key from our config
        first_key = config.openai.get_next_key()
        super().__init__(
            api_key=first_key,
            model=config.openai.embedding_model,
            max_retries=config.openai.max_retries,
            retry_min_seconds=config.openai.retry_min_seconds,
            retry_max_seconds=config.openai.retry_max_seconds,
            **kwargs
        )
        self._manager = openai_manager

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed multiple documents with rate limiting and data cleaning"""
        # Add jitter to avoid rate limits
        time.sleep(random.uniform(0, 3))

        # ✅ ADDED: Validate and clean texts
        cleaned_texts = []
        for i, text in enumerate(texts):
            if not text or not text.strip():
                logger.warning(f"Document {i} is empty, using placeholder text")
                cleaned_texts.append("Empty document placeholder")
            else:
                cleaned_text = strip_data_attributes(text).strip()
                if not cleaned_text:
                    logger.warning(f"Document {i} became empty after cleaning, using placeholder")
                    cleaned_texts.append("Empty document placeholder")
                else:
                    cleaned_texts.append(cleaned_text)

        logger.info(f"Embedding {len(cleaned_texts)} documents")

        try:
            return super().embed_documents(cleaned_texts)
        except RateLimitError:
            # Rotate key and retry once
            new_key = config.openai.get_next_key()
            logger.warning("Embedding rate limit hit; switching key and retrying")
            self.openai_api_key = new_key
            return super().embed_documents(cleaned_texts)

    def embed_query(self, text: str) -> List[float]:
        """Embed a single query with rate limiting and validation"""
        time.sleep(random.uniform(0, 1))

        # ✅ ADDED: Validate input text
        if not text or not text.strip():
            logger.warning(f"Empty text passed to embed_query, using placeholder")
            text = "Empty text placeholder"
        else:
            # Clean the text
            cleaned_text = strip_data_attributes(text).strip()
            if not cleaned_text:
                logger.warning(f"Text became empty after cleaning, using placeholder")
                text = "Empty text placeholder"
            else:
                text = cleaned_text

        # ✅ ADDED: Debug logging for problematic cases
        if len(text) < 10:
            logger.warning(f"Very short text for embedding: '{text[:50]}...'")
        
        logger.debug(f"Embedding text of length {len(text)}: '{text[:100]}...'")

        try:
            result = super().embed_query(text)
            
            # ✅ ADDED: Validate result
            if not result or len(result) == 0:
                logger.error(f"OpenAI returned empty embedding for text: '{text[:100]}...'")
                # Return a dummy vector with correct dimensions
                return [0.0] * 3072  # text-embedding-3-large dimensions
            
            logger.debug(f"Successfully generated embedding with {len(result)} dimensions")
            return result
            
        except RateLimitError:
            # Rotate key and retry once
            new_key = config.openai.get_next_key()
            logger.warning("Query embedding rate limit hit; switching key and retrying")
            self.openai_api_key = new_key
            
            result = super().embed_query(text)
            
            # ✅ ADDED: Validate result after retry
            if not result or len(result) == 0:
                logger.error(f"OpenAI returned empty embedding after retry for text: '{text[:100]}...'")
                return [0.0] * 3072
                
            return result
            
        except Exception as e:
            logger.error(f"Embedding failed for text: '{text[:100]}...', error: {e}")
            # Return dummy vector instead of failing
            logger.warning(f"Returning dummy vector due to embedding failure")
            return [0.0] * 3072

# Global embeddings instance
en_embeddings = RateLimitedOpenAIEmbeddings()

def get_embedding_size() -> int:
    """Get the embedding dimension for the configured model"""
    return config.qdrant.embedding_size