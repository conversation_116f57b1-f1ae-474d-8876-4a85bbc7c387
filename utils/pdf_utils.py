"""
Enhanced PDF processing utilities with improved HTML AST generation and chunking
"""

import json
import os
import re
import time
import random
import logging
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from pydantic import BaseModel, Field
import fitz
import html2text
from bs4 import BeautifulSoup

# from .config import config
from .openai_utils import openai_manager, client_openai

logger = logging.getLogger(__name__)

import json
import re
import requests  # new import to fetch PDFs from URLs
from bs4 import BeautifulSoup
import fitz

def parse_pdf_to_html_ast(pdf_path: str) -> BeautifulSoup:
    """
    Parse a PDF into an HTML AST with semantic structure.
    Now handles remote URLs and preserves hyperlinks as <a> tags.
    """
    # Open the PDF (from URL or local path)
    try:
        if pdf_path.lower().startswith("http"):
            resp = requests.get(pdf_path)
            resp.raise_for_status()
            pdf_bytes = resp.content
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        else:
            doc = fitz.open(pdf_path)
    except Exception as e:
        logger.error(f"Failed to open PDF {pdf_path}: {e}")
        raise

    try:
        # Determine the most common font size to identify body text size
        size_counts: Dict[float, int] = {}
        for page in doc:
            blocks = page.get_text("dict")["blocks"]
            for blk in blocks:
                if blk.get("type") != 0:  # text blocks only
                    continue
                for line in blk.get("lines", []):
                    for span in line.get("spans", []):
                        size = round(span.get("size", 0), 1)
                        size_counts[size] = size_counts.get(size, 0) + 1
        body_size = max(size_counts, key=size_counts.get) if size_counts else 12.0
        # Map larger font sizes to heading levels (h1 through h6)
        heading_sizes = sorted([s for s in size_counts.keys() if s > body_size], reverse=True)
        size_to_level = {size: idx+1 for idx, size in enumerate(heading_sizes[:6])}

        # Prepare BeautifulSoup root
        soup = BeautifulSoup("", "html.parser")
        root = soup.new_tag("div")
        soup.append(root)
        section_stack = [(root, 0)]  # stack of (current_section, level)

        # Pre-extract hyperlinks on each page
        links_by_page: Dict[int, List[Dict[str, str]]] = {}
        for page_index in range(len(doc)):
            page = doc[page_index]
            page_num = page_index + 1
            link_info_list = []
            for link in page.get_links():
                uri = link.get("uri")
                if uri:
                    rect = fitz.Rect(link["from"])  # rectangle area of the link
                    # Extract text within the link rectangle
                    link_text = (page.get_textbox(rect) or "").strip()
                    if link_text:
                        # Replace newlines with space to handle multi-line link text
                        link_text = link_text.replace("\n", " ")
                        link_info_list.append({"uri": uri, "text": link_text})
            links_by_page[page_num] = link_info_list

        # Parse content blocks and build HTML structure
        for page_index, page in enumerate(doc):
            page_num = page_index + 1
            blocks = page.get_text("dict")["blocks"]
            for blk in blocks:
                if blk.get("type") != 0:
                    continue  # skip non-text (images, drawings)
                lines = blk.get("lines", [])
                if not lines:
                    continue
                # Combine all spans text from each line (for structure decisions and initial text)
                all_spans = [span for line in lines for span in line.get("spans", [])]
                if not all_spans:
                    continue
                block_max_size = max(round(span.get("size", 0), 1) for span in all_spans)
                # Construct raw text of the block (lines joined by newline)
                lines_text = ["".join(span.get("text", "") for span in line.get("spans", [])) 
                              for line in lines]
                raw_text = "\n".join(lines_text).strip()

                if block_max_size > body_size:
                    # This block is a heading
                    level = size_to_level.get(block_max_size, 6)  # default to h6 if size not mapped
                    heading_tag = soup.new_tag(f"h{level}")
                    # Build heading content (preserve line breaks and insert links)
                    for i, line_text in enumerate(lines_text):
                        # Insert anchor tags for any link substrings in this line
                        link_parts = []
                        if links_by_page.get(page_num):
                            # Find all link texts that appear in line_text
                            occurrences = []
                            for link in links_by_page[page_num]:
                                lt = link["text"]
                                if lt: 
                                    for m in re.finditer(re.escape(lt), line_text):
                                        occurrences.append((m.start(), m.end(), link["uri"]))
                            # Sort by start index and build segments
                            occurrences.sort(key=lambda x: x[0])
                            curr_idx = 0
                            for start, end, uri in occurrences:
                                if curr_idx < start:
                                    link_parts.append(line_text[curr_idx:start])
                                # Create anchor tag for the linked substring
                                a_tag = soup.new_tag("a", href=uri)
                                a_tag.string = line_text[start:end]
                                link_parts.append(a_tag)
                                curr_idx = end
                            if curr_idx < len(line_text):
                                link_parts.append(line_text[curr_idx:])
                        else:
                            link_parts.append(line_text)
                        # Append parts to heading_tag
                        for part in link_parts:
                            heading_tag.append(part)
                        if i < len(lines_text) - 1:
                            heading_tag.append(soup.new_string("\n"))  # preserve line break
                    # Add attributes and place heading in the hierarchy
                    heading_tag["data-page"] = str(page_num)
                    heading_tag["data-bbox"] = json.dumps(blk.get("bbox", []))
                    # Create or find the appropriate section container for this heading level
                    while section_stack and section_stack[-1][1] >= level:
                        section_stack.pop()
                    parent_section = section_stack[-1][0]
                    new_section = soup.new_tag("section")
                    new_section["data-page"] = str(page_num)
                    new_section["data-bbox"] = json.dumps(blk.get("bbox", []))
                    parent_section.append(new_section)
                    new_section.append(heading_tag)
                    section_stack.append((new_section, level))
                else:
                    # This is body text (paragraph or list item)
                    parent_section = section_stack[-1][0]
                    flat_text = raw_text.replace("\n", " ").strip()
                    # Check for bullet/list item pattern
                    if re.match(r"^(\u2022|\*|-|\d+[\.\)])\s+", flat_text):
                        # Determine list type (ordered vs unordered)
                        list_tag_name = "ol" if re.match(r"^\d+[\.\)]\s+", flat_text) else "ul"
                        # Create a new list tag if not continuing the current one
                        last_elem = parent_section.contents[-1] if parent_section.contents else None
                        if not (last_elem and getattr(last_elem, "name", None) == list_tag_name):
                            list_tag = soup.new_tag(list_tag_name)
                            list_tag["data-page"] = str(page_num)
                            list_tag["data-bbox"] = json.dumps(blk.get("bbox", []))
                            parent_section.append(list_tag)
                        else:
                            list_tag = last_elem
                        # Create list item
                        li_tag = soup.new_tag("li")
                        # Insert anchors in the list item text if needed
                        parts = []
                        if links_by_page.get(page_num):
                            occurrences = []
                            for link in links_by_page[page_num]:
                                lt = link["text"]
                                if lt:
                                    for m in re.finditer(re.escape(lt), flat_text):
                                        occurrences.append((m.start(), m.end(), link["uri"]))
                            occurrences.sort(key=lambda x: x[0])
                            # Remove overlapping occurrences if any (to avoid duplicate anchoring)
                            filtered = []
                            prev_end = -1
                            for start, end, uri in occurrences:
                                if start < prev_end:
                                    continue  # skip if overlaps with previous link text
                                filtered.append((start, end, uri))
                                prev_end = end
                            curr_idx = 0
                            for start, end, uri in filtered:
                                if curr_idx < start:
                                    parts.append(flat_text[curr_idx:start])
                                a_tag = soup.new_tag("a", href=uri)
                                a_tag.string = flat_text[start:end]
                                parts.append(a_tag)
                                curr_idx = end
                            if curr_idx < len(flat_text):
                                parts.append(flat_text[curr_idx:])
                        else:
                            parts.append(flat_text)
                        # Build the list item HTML
                        for part in parts:
                            li_tag.append(part)
                        li_tag["data-page"] = str(page_num)
                        li_tag["data-bbox"] = json.dumps(blk.get("bbox", []))
                        list_tag.append(li_tag)
                    else:
                        # Normal paragraph text
                        p_tag = soup.new_tag("p")
                        parts = []
                        if links_by_page.get(page_num):
                            occurrences = []
                            for link in links_by_page[page_num]:
                                lt = link["text"]
                                if lt:
                                    for m in re.finditer(re.escape(lt), flat_text):
                                        occurrences.append((m.start(), m.end(), link["uri"]))
                            occurrences.sort(key=lambda x: x[0])
                            # Filter out overlapping link occurrences
                            filtered = []
                            prev_end = -1
                            for start, end, uri in occurrences:
                                if start < prev_end:
                                    continue
                                filtered.append((start, end, uri))
                                prev_end = end
                            curr_idx = 0
                            for start, end, uri in filtered:
                                if curr_idx < start:
                                    parts.append(flat_text[curr_idx:start])
                                a_tag = soup.new_tag("a", href=uri)
                                a_tag.string = flat_text[start:end]
                                parts.append(a_tag)
                                curr_idx = end
                            if curr_idx < len(flat_text):
                                parts.append(flat_text[curr_idx:])
                        else:
                            parts.append(flat_text)
                        for part in parts:
                            p_tag.append(part)
                        p_tag["data-page"] = str(page_num)
                        p_tag["data-bbox"] = json.dumps(blk.get("bbox", []))
                        parent_section.append(p_tag)
        return soup
    finally:
        # Ensure the document is closed to free resources
        doc.close()

def chunk_html_ast(soup: BeautifulSoup, max_chars: int = None, overlap_percentage: int = 15) -> List[Dict]:
    """
    Enhanced HTML AST chunking with configurable overlap and better boundary preservation.
    Avoids infinite loops by always making progress.
    Args:
        soup: The BeautifulSoup object representing the HTML AST.
        max_chars: The maximum character length for a chunk.
        overlap_percentage: The percentage of overlap between consecutive chunks.
    """
    if max_chars is None:
        max_chars = 6000

    overlap_chars = int(max_chars * (overlap_percentage / 100))

    chunks: List[Dict] = []
    div = soup.find("div")
    all_elements = list(div.find_all(recursive=False)) if div else list(soup.find_all(recursive=False))
    if not all_elements:
        logger.warning("HTML AST has no processable top-level elements.")
        return []

    i = 0
    n = len(all_elements)
    while i < n:
        current_chunk_html = ""
        start_i = i
        # Build chunk up to max_chars
        while i < n and len(current_chunk_html) + len(str(all_elements[i])) <= max_chars:
            current_chunk_html += str(all_elements[i])
            i += 1

        # If a single element is larger than max_chars, force it as a chunk
        if not current_chunk_html and i < n:
            current_chunk_html = str(all_elements[i])
            i += 1

        if current_chunk_html.strip():
            chunks.append({
                "content": current_chunk_html,
                "positions": extract_positions_from_html(current_chunk_html)
            })

        # Overlap logic: step back by overlap_chars worth of elements
        if i < n and overlap_chars > 0:
            overlap_len = 0
            overlap_start = i - 1
            while overlap_start >= 0 and overlap_len < overlap_chars:
                overlap_len += len(str(all_elements[overlap_start]))
                overlap_start -= 1
            # Next chunk starts after overlap_start
            i = max(overlap_start + 1, i)  # Ensure progress

    return chunks

def _split_large_section(section_elem: BeautifulSoup, max_chars: int) -> List[str]:
    """
    Split a large section element into smaller chunks while preserving structure
    """
    chunks = []
    current_chunk = ""

    # Keep the section header
    header = section_elem.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
    if header:
        header_html = str(header)
        current_chunk = f"<section>{header_html}"
    else:
        current_chunk = "<section>"

    # Process other elements in the section
    for elem in section_elem.find_all(recursive=False):
        if elem.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            continue  # Already handled

        elem_html = str(elem)
        if len(current_chunk) + len(elem_html) + 10 > max_chars:  # +10 for closing tag
            # Close current chunk and start new one
            current_chunk += "</section>"
            chunks.append(current_chunk)

            # Start new chunk with header if available
            if header:
                current_chunk = f"<section>{header_html}{elem_html}"
            else:
                current_chunk = f"<section>{elem_html}"
        else:
            current_chunk += elem_html

    # Close final chunk
    if current_chunk and not current_chunk.endswith("</section>"):
        current_chunk += "</section>"
        chunks.append(current_chunk)

    return chunks

def has_diagonal_withdrawn_watermark(pdf_path: str, size_threshold: float = 20.0, ratio_tolerance: float = 0.3) -> bool:
    doc = fitz.open(pdf_path)
    for page in doc:
        for block in page.get_text("dict")["blocks"]:
            if block["type"] == 0:  # text
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip().lower()
                        if "withdrawn" in text and span["size"] >= size_threshold:
                            w = span["bbox"][2] - span["bbox"][0]
                            h = span["bbox"][3] - span["bbox"][1]
                            if h == 0:
                                continue
                            ratio = w / h
                            if abs(ratio - 1.0) < ratio_tolerance:
                                return True
    return False

# -------------------- Pydantic Models for Extraction --------------------
class Position(BaseModel):
    page_number: int = Field(..., description="The page number from which this item was extracted referred to data-page")
    bboxes: List[List[float]] = Field(..., description="List of Bounding box [[x1, y1, x2, y2],[x1, y1, x2, y2]] of all content of RBI regulation HTML Fragement(data-bbox list) considered as the guideline ")

class Obligation(BaseModel):
    guideline: str = Field(..., description="Exact paragraph or sentence considered as guideline from RBI regulation HTML Fragement, this will be used to extract the obligation")
    positions: List[Position] = Field(..., description="Positional information of the guideline page and bounding boxes of the guideline text")
    obligation: str = Field(..., description="As Complaiance office, The extracted or paraphrased guideline as obligation ")
    action_item: str = Field(..., description="A concise, actionable item derived from the obligation.")

class ObligationList(BaseModel):
    obligations_list: List[Obligation] = Field(..., description="A list of all extracted obligations.")

# -------------------- Extraction Functions & Prompts --------------------
def extract_obligations(html_chunks: List[str]) -> List[dict]:
    all_obls = []
    for chunk in html_chunks:
        prompt = (
            "Role: You are an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items\n"
            "Your task is to extract all the guidelines and obligations from guidelines, action_items from obligations from the provided RBI regulation HTML fragment.\n\n"
            "Goal: Extract a JSON array where each object contains exactly these fields:\n"
            "  - 'guideline' - identified sentence or paragraph as a instruction to the bank\n "
            "  - 'positions' - poistion of the guildeline, extracted from data-page and data-bbox of the exact/complete sentence or paragraph considered as guideline\n"
            "  - 'obligation'- rephrased guideline with the context of complaince officer, usually start with 'Bank Shall\n"
            "  - 'action_item' - rephrased obligations as a list of action items to be taken by the bank'\n\n"
            """
            Below are the sample Guidelines from a RBI Regulation, and corresponding generated/rephrased Obligation
                Guideline   : Notes determined as counterfeit shall be stamped as \"COUNTERFEIT NOTE\"
                Obligation  : Bank shall ensure that the Notes determined as counterfeit shall be stamped as \"COUNTERFEIT NOTE\" and impounded in the prescribed format (Annex I).

                Guideline   : Such impounded note shall be recorded under authentication, in a separate register.
                Obligation  : Bank shall ensure that the impounded notes shall be recorded under authentication, in a separate register.

                Guideline   : \"When a banknote tendered at the counter of a bank branch / back office and currency chest or treasury is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer.\"
                Obligation   : Bank shall ensure that when a banknote tendered at the counter of a bank branch is found to be counterfeit, an acknowledgement receipt in the prescribed format (Annex II) must be issued to the tenderer, after stamping the note. The receipt, in running serial numbers, should be authenticated by the cashier and tenderer.

                Guideline   : Detection of Counterfeit Notes - Reporting to Police and other bodies
                Obligation  : \"Bank shall ensure to report to Police and other bodies on Detection of Counterfeit Notes.\n                                The following procedure shall be followed while reporting incidence of detection of Counterfeit Note to the Police: \n                                1.  For cases of detection of Counterfeit Notes up to four (04) pieces in a single transaction, a consolidated report in the prescribed format (Annex III) shall be sent by the Nodal Bank Officer to the police authorities or the Nodal Police Station, along with the suspect Counterfeit Notes, at the end of the month.  \n                                2.  For cases of detection of Counterfeit Notes of five (05) or more pieces in a single transaction, the Counterfeit Notes shall be forwarded immediately by the Nodal Bank Officer to the local police authorities or the Nodal Police Station for investigation by filing FIR in the prescribed format (Annex IV).  \n                                3.  A copy of the monthly consolidated report / FIR shall be sent to the Forged Note Vigilance Cell constituted at the Head Office of the bank.  "
                Guideline   : Acknowledgement receipt - Notice to this effect should be displayed prominently at the offices / branches for information of the public
                Obligation  : Bank shall display a notice on availability of the Acknowledgement receipt to customers prominently at the offices / branches for information of the public.
            """

            "Instructions:\n"
            "1. Locate each paragraph/section containing guidelines (keywords: 'shall', 'must', 'required to') which can be percieved as instructions to banks\n"
            "3. Output only a JSON array of objects with those three keys, no extras.\n\n"
            "4. RBI Regulation as HTML contains position information as data-page_number and data-bbox atrributes for each guideline"
            "4. Make sure the Position information is accurately extracted for the each Guideline observed using  data-page_number and data-bbox atrributes"
            "5. Make sure understand the guideline and generate obligation similar to rephrasing pattern of the samples shared in the same context"
            "6. Use obligation to generate action items for the bank"
            "7. Always make sure to track and generate Position infromation for each Guideline using data-page_number and data-bbox atrributes "
            f"RBI Regulation as HTML:\n{chunk}"
        )
        completion = openai_manager.with_key_rotation(
            client_openai.beta.chat.completions.parse,
            model="gpt-4.1-mini-2025-04-14",
            messages=[
                {"role": "system", "content": "Extract structured obligations from RBI HTML docs as an expert compliance analyst with extensive experience in dissimilating RBI regulatory documents into guildelines, obligations and action items"},
                {"role": "user",   "content": prompt},
            ],
            response_format=ObligationList,
        )
        obls = completion.choices[0].message.parsed.obligations_list
        for o in obls:
            all_obls.append(o.dict())
    return all_obls

# -------------------- HTML Utility Functions --------------------
def strip_data_attributes(html: str) -> str:
    """
    Remove all data-* attributes from HTML content and optionally convert to markdown
    """
    soup = BeautifulSoup(html, "html.parser")
    for tag in soup.find_all():
        attrs_to_remove = [attr for attr in tag.attrs if attr.startswith("data-")]
        for attr in attrs_to_remove:
            del tag.attrs[attr]

    cleaned_html = str(soup)
    try:
        # Convert to markdown for better readability
        return html2text.html2text(cleaned_html)
    except ImportError:
        # Fallback to cleaned HTML if html2text not available
        return cleaned_html

def extract_positions_from_html(html: str) -> List[Dict]:
    soup = BeautifulSoup(html, "html.parser")
    positions: List[Dict] = []
    for tag in soup.find_all(attrs={"data-page": True}):
        try:
            page_num = int(tag["data-page"])
        except (ValueError, TypeError):
            page_num = None
        bbox = json.loads(tag.get("data-bbox", "[]"))
        positions.append({"page": page_num, "bbox": bbox})
    return positions

# -------------------- Recursive Split for Summarization --------------------
def recursive_split(text: str, max_len: int = 50000) -> List[str]:
    if len(text) <= max_len:
        return [text]
    mid = len(text) // 2
    split_point = text.rfind(". ", 0, mid)
    if split_point == -1:
        split_point = mid
    return recursive_split(text[:split_point], max_len) + recursive_split(text[split_point:], max_len)

# -------------------- Summarization & Topic Extraction --------------------
def summarize_and_extract(chunk: str, max_words: int = 150, max_topics: int = 5) -> Dict[str, Any]:
    class SummaryAndTopics(BaseModel):
        summary: str
        topics: List[str]
    prompt = f"""
You are an AI Compliance Officer Agent specializing in RBI regulations and guidelines.

QUERY: Provide a JSON object with two fields:
  - \"summary\": a concise overview in strictly {max_words} maximum words
  - \"topics\": up to {max_topics} key topics from the content

CONTEXT:
{chunk}
"""
    completion = openai_manager.with_key_rotation(
        client_openai.beta.chat.completions.parse,
        model="gpt-4.1-nano-2025-04-14",
        messages=[
            {"role":"system","content":"You are a regulatory compliance summarizer and topic extractor for RBI guidelines."},
            {"role":"user","content":prompt}
        ],
        response_format=SummaryAndTopics,
    )
    data: SummaryAndTopics = completion.choices[0].message.parsed
    return data.dict()

# -------------------- Document Code Extraction --------------------
def extract_document_code(pdf_path):
    """
    Enhanced document code extraction with robust patterns and fallback mechanisms
    """
    def extract_text_from_pdf(pdf_path):
        doc = fitz.open(pdf_path)
        try:
            full_text = ""
            pages_to_extract = min(3, doc.page_count)  # Check more pages
            for page_number in range(pages_to_extract):
                page = doc.load_page(page_number)
                full_text += page.get_text()
            return full_text
        finally:
            doc.close()

    text = extract_text_from_pdf(pdf_path)

    # Enhanced patterns for document code extraction
    enhanced_patterns = [
        # Primary RBI patterns
        r"^\s*(RBI/\d{4}-\d{2}/\d+.*?Circular\s+No\.?\s*\d+)",
        r"^\s*(RBI/\d{4}-\d{2}/\d+)",

        # Department code patterns
        r"^\s*([A-Z]{2,4}\.[A-Z]{2,4}\.[A-Z]{1,4}\.[A-Z]*\d+/[\d\.]+/\d{4}-\d{2})",
        r"^\s*([A-Z]{2,4}\.[A-Z]{2,4}\.[A-Z]{1,4}\.\d+/[\d\.]+/\d{4}-\d{2})",

        # Circular number patterns
        r"Circular\s+No\.?\s*([A-Z]*\d+)",
        r"Master\s+Direction\s+No\.?\s*([A-Z]*\d+)",

        # Fallback patterns
        r"([A-Z]{2,4}\.No\.[A-Z\.]+\d+/[\d\.]+/\d{4}-\d{2})",
        r"(No\.[A-Z\.]+\d+/[\d\.]+/\d{4}-\d{2})"
    ]

    # Try each pattern in order of preference
    for pattern in enhanced_patterns:
        matches = re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE)
        for match in matches:
            code = match.group(1).strip()
            if len(code) > 5:  # Basic validation
                return _normalize_document_code(code)

    return "Not Found"

def _normalize_document_code(code):
    """
    Normalize document codes for consistency
    """
    if not code:
        return "Not Found"

    # Remove extra whitespace
    code = re.sub(r'\s+', ' ', code.strip())

    # Standardize common patterns
    code = re.sub(r'Circular\s+No\.?\s*', 'Circular No. ', code, flags=re.IGNORECASE)
    code = re.sub(r'Master\s+Direction\s+No\.?\s*', 'Master Direction No. ', code, flags=re.IGNORECASE)

    return code


def extract_full_notification_codes(pdf_path):
    """
    Extract both short and long notification codes from PDF headers.

    Returns:
        dict: {
            'short_code': str,  # e.g., 'RBI/DNBR/2016-17/42'
            'long_code': str,   # e.g., '.PD.004/03.10.119/2016-17'
            'full_code': str,   # Combined code if both found
            'year': str,        # Extracted year from codes
            'all_codes': list   # All codes found in document
        }
    """
    def extract_text_from_pdf(pdf_path):
        doc = fitz.open(pdf_path)
        try:
            full_text = ""
            pages_to_extract = min(3, doc.page_count)  # Check first 3 pages
            for page_number in range(pages_to_extract):
                page = doc.load_page(page_number)
                full_text += page.get_text()
            return full_text
        finally:
            doc.close()

    try:
        text = extract_text_from_pdf(pdf_path)

        # Initialize result structure
        result = {
            'short_code': '',
            'long_code': '',
            'full_code': '',
            'year': '',
            'all_codes': []
        }

        # Pattern for short codes (RBI reference numbers)
        short_code_patterns = [
            r'RBI/\d{4}-\d{2}/\d+',           # RBI/2025-26/64
            r'RBI/[A-Z]+/\d{4}-\d{2}/\d+',    # RBI/DNBR/2016-17/42
            r'FEMA\s*\d+\([A-Z]*\)/\(\d+\)/\d{4}-[A-Z]+',  # FEMA 23(R)/(6)/2025-RB
            r'FEMA\s*\d+\([A-Z]*\)/\d{4}-[A-Z]+',          # FEMA 23(R)/2015-RB
        ]

        # Pattern for long codes (department codes)
        long_code_patterns = [
            r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',  # DoR.MCS.REC.38/01.01.001/2025-26
            r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}',        # DBOD.No.Leg.BC.21/09.07.007/2002
            r'\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',                # .PD.004/03.10.119/2016-17
            r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+',              # DPSS.CO.PD.No.123/02.14.003
        ]

        # Extract short codes
        for pattern in short_code_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                result['all_codes'].append(match)
                if not result['short_code']:  # Take first match as primary
                    result['short_code'] = match

        # Extract long codes
        for pattern in long_code_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                result['all_codes'].append(match)
                if not result['long_code']:  # Take first match as primary
                    result['long_code'] = match

        # Extract year from codes
        year_matches = re.findall(r'\d{4}-\d{2}', ' '.join(result['all_codes']))
        if year_matches:
            # Convert 2025-26 format to 2025
            year_part = year_matches[0].split('-')[0]
            result['year'] = year_part
        else:
            # Fallback: look for 4-digit years in codes
            year_matches = re.findall(r'\d{4}', ' '.join(result['all_codes']))
            if year_matches:
                # Take the most recent year that makes sense (2020-2030 range)
                valid_years = [y for y in year_matches if 2020 <= int(y) <= 2030]
                if valid_years:
                    result['year'] = valid_years[0]

        # Create full code by combining short and long codes
        if result['short_code'] and result['long_code']:
            result['full_code'] = f"{result['short_code']}; {result['long_code']}"
        elif result['short_code']:
            result['full_code'] = result['short_code']
        elif result['long_code']:
            result['full_code'] = result['long_code']

        # Remove duplicates from all_codes
        result['all_codes'] = list(set(result['all_codes']))

        return result

    except Exception as e:
        logger.error(f"Error extracting notification codes from {pdf_path}: {e}")
        return {
            'short_code': '',
            'long_code': '',
            'full_code': '',
            'year': '',
            'all_codes': []
        }
