import json
import logging
from pathlib import Path
from mistralai import Mistral, DocumentURLChunk, TextChunk
from models.pdf_parser import *

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Mistral client
api_key = "GPbx63u3tDIExobMILyh8encdTFFFqPO"  # Replace with your API key or use Airflow Variables
client = Mistral(api_key=api_key)

def upload_pdf(pdf_path: str) -> str:
    """Upload the PDF to Mistral's OCR service and return the file ID."""
    logger.info(f"Starting PDF upload process for file: {pdf_path}")
    pdf_file = Path(pdf_path)
    assert pdf_file.is_file(), f"PDF file not found: {pdf_path}"
    
    # Read file content with explicit binary mode
    with pdf_file.open('rb') as f:
        file_content = f.read()
    
    logger.info(f"Uploading file {pdf_file.name} to Mistral OCR service")
    try:
        uploaded_file = client.files.upload(
            file={"file_name": pdf_file.name,  # Use full filename with extension
                  "content": file_content},
            purpose="ocr"
        )
        logger.info(f"Successfully uploaded file. File ID: {uploaded_file.id}")
        return uploaded_file.id
    except Exception as e:
        logger.error(f"Failed to upload PDF: {str(e)}")
        raise

def process_ocr(file_id: str) -> str:
    """Process OCR for the entire document and return combined Markdown."""
    logger.info(f"Starting OCR processing for file ID: {file_id}")
    try:
        signed_url = client.files.get_signed_url(file_id=file_id, expiry=1)
        logger.info("Successfully obtained signed URL for file ID: {file_id}: {signed_url.url}")
        
        pdf_response = client.ocr.process(
            document=DocumentURLChunk(document_url=signed_url.url),
            model="mistral-ocr-latest",
            include_image_base64=True
        )
        logger.info("Successfully processed OCR")
        
        # Combine all pages into a single Markdown string
        combined_markdown = get_combined_markdown(pdf_response)
        logger.info("Successfully combined markdown from all pages")
        return combined_markdown
    except Exception as e:
        print(
            e
        )
        logger.error(f"Error during OCR processing: {str(e)}")
        raise

def get_combined_markdown(ocr_response) -> str:
    """Combine OCR text and images from all pages into a single Markdown string."""
    logger.info("Starting markdown combination process")
    markdowns = []
    for i, page in enumerate(ocr_response.pages, 1):
        logger.debug(f"Processing page {i}")
        image_data = {img.id: img.image_base64 for img in page.images}
        markdowns.append(replace_images_in_markdown(page.markdown, image_data))
    logger.info(f"Successfully processed {len(markdowns)} pages")
    return "\n\n".join(markdowns)

def replace_images_in_markdown(markdown_str: str, images_dict: dict) -> str:
    """Replace image placeholders with base64-encoded images in Markdown."""
    logger.debug(f"Replacing {len(images_dict)} images in markdown")
    for img_name, base64_str in images_dict.items():
        markdown_str = markdown_str.replace(
            f"![{img_name}]({img_name})", f"![{img_name}]({base64_str})"
        )
    return markdown_str

def generate_structured_output(combined_markdown: str) -> dict:
    """Generate structured JSON output from the combined Markdown."""
    logger.info("Starting structured output generation")
    
    # Example data to guide the AI
    example_data ={
        "circular_number": "RBI/2024-25/123",
        "circular_title": "Master Direction on Counterfeit Notes",
        "date_of_issue": "01/04/2024",
        "document_index": [
            "3. Impounding of Counterfeit Notes",
            "4. Penalties",
            "5. Training",
            "6. Reporting Deadlines",
            "7. Equipment Standards"
        ],
        "pages": [
            {
            "page_number": 1,
            "content_summary": "Impounding, penalties, training, reporting, and equipment standards",
        "paragraphs": [
            # Existing Example 1: Strict Obligation with "shall"
            {
                "para_number": "3. Impounding of Counterfeit Notes",
                # "para_verbatim": "Notes determined as counterfeit shall be stamped as 'COUNTERFEIT NOTE' and impounded as per the process in Annex II. See Figure 1 for stamping example.",
                "content_type": "text",
                "obligation": "Notes determined as counterfeit shall be stamped as 'COUNTERFEIT NOTE' and impounded as per the process in Annex II.",
                "directives": "Stamp the note as 'COUNTERFEIT NOTE', follow the process in Annex II.",
                "prohibitions": None,
                "consequences": None,
                "action_point": "Bank Shall ensure counterfeit notes are stamped and impounded correctly, following the process in Annex II.",
                "action_items": [
                    {
                        "item_type": "procedure",
                        "description": "Implement the stamping and impounding process as per Annex II",
                        "linked_files": ["annex2.pdf"]
                    },
                    {
                        "item_type": "reference",
                        "description": "Refer to Figure 1 for stamping example",
                        "linked_files": ["figure1.jpg"]
                    }
                ]
            },
            # Existing Example 2: Obligation with "must not"
            {
                "para_number": "4. Penalties",
                # "para_verbatim": "Penalties for non-compliance are detailed in Table 1. Banks must not delay in reporting violations.",
                "content_type": "text",
                "obligation": "Banks must not delay in reporting violations.",
                "directives": "Report violations without delay.",
                "prohibitions": "Do not delay in reporting violations.",
                "consequences": "Penalties as per Table 1",
                "action_point": "Bank shall review and update internal policies to align with the penalty structure in Table 1 and ensure timely reporting.",
                "action_items": [
                    {
                        "item_type": "policy",
                        "description": "Update compliance policies based on Table 1",
                        "linked_files": []
                    },
                    {
                        "item_type": "procedure",
                        "description": "Establish a process to ensure timely reporting of violations",
                        "linked_files": []
                    }
                ]
            },
            # Existing Example 3: Obligation with "must"
            {
                "para_number": "5. Training",
                # "para_verbatim": "Banks must conduct training for staff on counterfeit detection and impounding procedures at least annually, covering topics in Annex III.",
                "content_type": "text",
                "obligation": "Banks must conduct training for staff on counterfeit detection and impounding procedures at least annually, covering topics in Annex III.",
                "directives": "Conduct training at least annually, cover topics in Annex III.",
                "prohibitions": None,
                "consequences": None,
                "action_point": "Schedule annual training sessions on counterfeit detection and impounding.",
                "action_items": [
                    {
                        "item_type": "training",
                        "description": "Develop and schedule annual training program based on Annex III",
                        "linked_files": ["annex3.pdf"]
                    },
                    {
                        "item_type": "documentation",
                        "description": "Document training attendance and topics covered",
                        "linked_files": []
                    }
                ]
            },
            # Existing Example 4: Obligation with "shall"
            {
                "para_number": "6. Reporting Deadlines",
                # "para_verbatim": "Banks shall submit quarterly reports on counterfeit incidents as per the format in Annex IV by the 15th of the following month. See Table 2 for deadlines.",
                "content_type": "text",
                "obligation": "Banks shall submit quarterly reports on counterfeit incidents as per the format in Annex IV by the 15th of the following month.",
                "directives": "Submit quarterly reports by the 15th of the following month, use format in Annex IV.",
                "prohibitions": None,
                "consequences": None,
                "action_point": "Implement quarterly reporting as specified.",
                "action_items": [
                    {
                        "item_type": "reporting",
                        "description": "Prepare and submit quarterly reports using the format in Annex IV",
                        "linked_files": ["annex4.pdf"]
                    },
                    {
                        "item_type": "reference",
                        "description": "Adhere to deadlines outlined in Table 2",
                        "linked_files": []
                    }
                ]
            },
            # Existing Example 5: Obligation with "must"
            {
                "para_number": "7. Equipment Standards",
                # "para_verbatim": "Banks must use approved note-sorting machines as depicted in Figure 2. Non-compliant equipment is prohibited.",
                "content_type": "text",
                "obligation": "Banks must use approved note-sorting machines as depicted in Figure 2.",
                "directives": "Use approved note-sorting machines as shown in Figure 2.",
                "prohibitions": "Do not use non-compliant equipment.",
                "consequences": None,
                "action_point": "Ensure all note-sorting machines meet the standards shown in Figure 2.",
                "action_items": [
                    {
                        "item_type": "equipment",
                        "description": "Verify and upgrade note-sorting machines to match Figure 2 specifications",
                        "linked_files": ["figure2.jpg"]
                    },
                    {
                        "item_type": "audit",
                        "description": "Conduct an audit to identify and remove non-compliant equipment",
                        "linked_files": []
                    }
                ]
            },
            # New Example 6: Recommendation (No "shall" or "must")
            {
                "para_number": "8. Best Practices",
                # "para_verbatim": "Banks are encouraged to adopt advanced counterfeit detection technologies.",
                "content_type": "text",
                "obligation": None,
                "directives": "Adopt advanced counterfeit detection technologies.",
                "prohibitions": None,
                "consequences": None,
                "action_point": "Consider implementing advanced counterfeit detection technologies.",
                "action_items": [
                    {
                        "item_type": "technology",
                        "description": "Evaluate and potentially adopt new detection technologies",
                        "linked_files": []
                    }
                ]
            },
            # New Example 7: Implicit Directive (Deadline without "shall")
            {
                "para_number": "9. Submission Timeline",
                # "para_verbatim": "Reports are to be submitted by the end of each quarter.",
                "content_type": "text",
                "obligation": "Reports are to be submitted by the end of each quarter.",
                "directives": "Submit reports by the end of each quarter.",
                "prohibitions": None,
                "consequences": None,
                "action_point": "Ensure quarterly reports are submitted on time.",
                "action_items": [
                    {
                        "item_type": "reporting",
                        "description": "Set reminders for quarterly report submissions",
                        "linked_files": []
                    }
                ]
            }
        ],
            "tables": [
                {
                "table_number": "Table 1",
                "table_summary": "Penalties for Non-Compliance",
                "data": [
                    {"offense": "Failure to report", "penalty": "Rs. 10,000", "additional": "Warning"},
                    {"offense": "Improper stamping", "penalty": "Rs. 5,000", "additional": "Training required"}
                ]
                },
                {
                "table_number": "Table 2",
                "table_summary": "Reporting Deadlines",
                "data": [
                    {"quarter": "Q1 (Jan-Mar)", "submission_deadline": "15th April"},
                    {"quarter": "Q2 (Apr-Jun)", "submission_deadline": "15th July"},
                    {"quarter": "Q3 (Jul-Sep)", "submission_deadline": "15th October"},
                    {"quarter": "Q4 (Oct-Dec)", "submission_deadline": "15th January"}
                ]
                }
            ],
            "images": [
                {
                "image_number": "Figure 1",
                "image_description": "Stamping Example: A banknote with 'COUNTERFEIT NOTE' stamped across it in red ink.",
                "file": "figure1.jpg"
                },
                {
                "image_number": "Figure 2",
                "image_description": "Approved Note-Sorting Machine: A diagram of a note-sorting machine with labeled components: input tray, scanner, and output bin.",
                "file": "figure2.jpg"
                }
            ]
            }
        ],
        "index_summary": {
            "3. Impounding of Counterfeit Notes": "Process for impounding counterfeit notes",
            "4. Penalties": "Details on penalties for non-compliance",
            "5. Training": "Training requirements for bank staff",
            "6. Reporting Deadlines": "Deadlines for submitting quarterly reports",
            "7. Equipment Standards": "Standards for note-sorting machines"
        },
        "linked_documents": ["annex2.pdf", "figure1.jpg", "annex3.pdf", "annex4.pdf", "figure2.jpg"]
        }
    
    # Prompt with enhanced structure guidance
    prompt = (
        "Analyze the following document and generate a structured JSON output with the following REQUIRED FIELDS:\n"
        "1. 'circular_number' (string)\n"
        "2. 'circular_title' (string)\n"
        "3. 'date_of_issue' (string in DD/MM/YYYY format)\n"
        "4. 'document_index' (array of strings)\n"
        "5. 'pages' (array of page objects with paragraphs, tables, images)\n\n"
        "Instructions for generating action points:\n"
        "- Identify paragraphs containing directives, instructions, requirements, or recommendations\n"
        "- Look for phrases indicating obligations ('shall', 'must', 'should', 'required to', 'expected to')\n"
        "- Consider implicit directives or tasks without explicit obligation words\n"
        "- Generate one action point per exact obligation phrase\n"
        "- Split multi-location requirements into separate action items\n"
        "- Reference all relevant annexes, figures, or tables mentioned\n"
        "- Preserve Rs. symbol in penalties\n"
        "- Maintain original para_number formats\n\n"
        "Important elements to capture:\n"
        "- Master Directions and Notifications\n"
        "- Deadlines and reporting requirements\n"
        "- Penalties and consequences\n"
        "- Technology recommendations\n"
        "- Implicit deadlines\n\n"
        "Document Content:\n"
        f"{combined_markdown}\n\n"
        "Example Output Structure:\n"
        f"{json.dumps(example_data, indent=2)}\n\n"
        "Generate JSON matching this exact structure, preserving all existing patterns:"
    )

    try:
        response = client.chat.complete(
            model="pixtral-12b-latest",
            messages=[{"role": "user", "content": prompt}],
            response_format={"type": "json_object"},
            temperature=0.2
        )
        
        # Add validation debug logging
        logger.debug(f"Raw API response: {response.choices[0].message.content}")
        validated_data = StructuredOutput.parse_raw(response.choices[0].message.content)
        logger.info("Validation succeeded")
        return validated_data.dict()
    except Exception as e:
        logger.error(f"Validation failed: {str(e)}")
        raise
