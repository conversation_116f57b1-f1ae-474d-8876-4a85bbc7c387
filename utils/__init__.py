import os
import sys
from pathlib import Path

# Add project paths to PYTHONPATH
project_root = Path(__file__).parent.parent.absolute()
complai_path = project_root / 'complai_knowledge_tracker' / 'airflow' / 'dags'
paths_to_add = [
    str(project_root),
    str(complai_path),
    str(project_root / 'utils'),
]

for path in paths_to_add:
    if path not in sys.path and os.path.exists(path):
        sys.path.insert(0, path)

# Create __all__ to expose commonly used imports
__all__ = ['setup_paths', 'get_project_root']

def setup_paths():
    """Ensure all necessary paths are in sys.path"""
    return all(path in sys.path for path in paths_to_add)

def get_project_root():
    """Get the absolute path to the project root"""
    return str(project_root)