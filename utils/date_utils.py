from datetime import datetime, date
from dateutil import parser
from typing import Union, Dict, Any
import logging

logger = logging.getLogger(__name__)


def normalize_date(date_str: str) -> str:
    """
    Normalize date strings to ISO format (YYYY-MM-DD).
    Handles various input formats like DD/MM/YYYY, MM/DD/YYYY, etc.

    Args:
        date_str: A string representing a date in various formats

    Returns:
        Normalized date string in YYYY-MM-DD format, or None if parsing fails
    """
    if not date_str:
        return None

    # Common date formats in RBI documents
    date_formats = [
        '%d/%m/%Y',  # DD/MM/YYYY (most common in Indian format)
        '%d-%m-%Y',  # DD-MM-YYYY
        '%d.%m.%Y',  # DD.MM.YYYY
        '%B %d, %Y', # Month DD, YYYY (e.g., January 01, 2023)
        '%d %B %Y',  # DD Month YYYY (e.g., 01 January 2023)
        '%d %b %Y',  # DD Mon YYYY (e.g., 01 Jan 2023)
        '%b %d, %Y', # Mon DD, YYYY (e.g., Jan 01, 2023)
        '%Y-%m-%d',  # YYYY-MM-DD (ISO format, already normalized)
        '%m/%d/%Y',  # MM/DD/YYYY (US format)
    ]

    # Try each format until one works
    for fmt in date_formats:
        try:
            parsed_date = datetime.strptime(date_str.strip(), fmt)
            return parsed_date.strftime('%Y-%m-%d')  # Return in ISO format
        except ValueError:
            continue

    # If we get here, none of the formats worked
    return None


def standardize_date_for_storage(date_input: Union[str, datetime, Dict[str, Any]]) -> str:
    """
    Standardize any date input to YYYY-MM-DD string format for consistent storage.
    This replaces MongoDB's extended JSON date format with simple string dates.

    Args:
        date_input: Can be:
            - String date in various formats
            - datetime object
            - MongoDB extended JSON format like {"$date": "2025-06-16T19:25:00"}
            - ISO string like "2025-06-16T19:25:00"

    Returns:
        Standardized date string in YYYY-MM-DD format, or None if parsing fails
    """
    if not date_input:
        return None

    try:
        # Handle MongoDB extended JSON format
        if isinstance(date_input, dict) and "$date" in date_input:
            date_str = date_input["$date"]
            if isinstance(date_str, str):
                dt = parser.parse(date_str)
                return dt.strftime('%Y-%m-%d')
            elif isinstance(date_str, datetime):
                return date_str.strftime('%Y-%m-%d')

        # Handle datetime objects
        elif isinstance(date_input, datetime):
            return date_input.strftime('%Y-%m-%d')

        # Handle date objects
        elif isinstance(date_input, date):
            return date_input.strftime('%Y-%m-%d')

        # Handle string inputs
        elif isinstance(date_input, str):
            # First try with dateutil parser for flexibility
            try:
                dt = parser.parse(date_input)
                return dt.strftime('%Y-%m-%d')
            except:
                # Fall back to normalize_date for specific formats
                return normalize_date(date_input)

        else:
            logger.warning(f"Unsupported date input type: {type(date_input)}")
            return None

    except Exception as e:
        logger.error(f"Error standardizing date '{date_input}': {str(e)}")
        return None


def create_date_query_filter(date_value: Union[str, datetime, date], operator: str = "$gte") -> Dict[str, Any]:
    """
    Create a MongoDB query filter for date comparisons that works with standardized YYYY-MM-DD string dates.

    Args:
        date_value: The date to compare against (string, datetime, or date object)
        operator: MongoDB comparison operator ("$gte", "$lte", "$gt", "$lt", "$eq")

    Returns:
        Dictionary suitable for MongoDB queries

    Example:
        # For finding documents published in the last week
        one_week_ago = datetime.now() - timedelta(days=7)
        filter_dict = create_date_query_filter(one_week_ago, "$gte")
        # Returns: {"$gte": "2025-06-16"}
    """
    standardized_date = standardize_date_for_storage(date_value)
    if standardized_date:
        return {operator: standardized_date}
    else:
        logger.error(f"Could not create date filter for: {date_value}")
        return {}


def convert_legacy_date_field(date_field: Union[str, datetime, Dict[str, Any]]) -> str:
    """
    Convert legacy date fields (like MongoDB extended JSON) to standardized YYYY-MM-DD format.
    This is useful for migration scripts and data cleanup.

    Args:
        date_field: Legacy date field in any format

    Returns:
        Standardized date string in YYYY-MM-DD format
    """
    return standardize_date_for_storage(date_field)
