"""
Defines core PDF processing functionalities for the pipeline.
"""

import fitz  # PyMuPDF
import logging
from typing import Dict, List, Tuple, Optional
from bs4 import BeautifulSoup
from pathlib import Path
import re

logger = logging.getLogger(__name__)

def is_diagonal(bbox: fitz.Rect, tol: float = 0.3) -> bool:
    """Check if a bounding box represents diagonal text (likely a watermark)"""
    w = bbox.x1 - bbox.x0
    h = bbox.y1 - bbox.y0
    if h == 0:
        return False
    ratio = w / h
    return abs(ratio - 1.0) < tol

def check_pdf_watermark(pdf_path: str, size_threshold: float = 20.0, ratio_tolerance: float = 0.3) -> str:
    """
    Check if a PDF has diagonal watermarks indicating its status.
    
    Args:
        pdf_path: Path to the PDF file
        size_threshold: Minimum font size to consider (default: 20.0)
        ratio_tolerance: Tolerance for diagonal ratio check (default: 0.3)
    
    Returns:
        str: Status - "WITHDRAWN", "SUPERSEDED", "REPEALED", or "ACTIVE"
    """
    try:
        doc = fitz.open(pdf_path)
        keywords = ["withdrawn", "superseded", "repealed"]
        
        for pno, page in enumerate(doc, start=1):
            raw = page.get_text("rawdict")
            for block in raw["blocks"]:
                if block["type"] != 0:  # Skip non-text blocks
                    continue
                for line in block["lines"]:
                    for span in line["spans"]:
                        # Get text from span
                        text = "".join(ch["c"] for ch in span["chars"])
                        norm = text.lower().replace(" ", "").replace("\n", "")
                        
                        # Check for keywords
                        keyword_found = None
                        for keyword in keywords:
                            if keyword in norm:
                                keyword_found = keyword
                                break
                        
                        if not keyword_found:
                            continue

                        # Check font size and orientation
                        size = span["size"]
                        if size < size_threshold:
                            continue

                        if not is_diagonal(fitz.Rect(span["bbox"]), tol=ratio_tolerance):
                            continue

                        # Found a valid watermark
                        logger.info(f"→ Found diagonal '{keyword_found.upper()}' on page {pno} "
                                  f"(size={size:.1f}, bbox={span['bbox']})")
                        doc.close()
                        return keyword_found.upper()
        
        doc.close()
        return "ACTIVE"
    except Exception as e:
        logger.error(f"Error checking watermark in {pdf_path}: {e}")
        return "UNKNOWN"

def extract_notification_codes(text: str) -> Dict[str, str]:
    """
    Extract notification codes from PDF text with enhanced patterns.
    """
    try:
        result = {
            'short_code': '',
            'long_code': '',
            'full_code': '',
            'year': '',
            'all_codes': []
        }
        
        # Enhanced patterns for different code formats
        patterns = {
            'short': [
                r'RBI/\d{4}-\d{2}/\d+',
                r'RBI/[A-Z]+/\d{4}-\d{2}/\d+',
                r'FEMA\s*\d+\([A-Z]*\)/\d+/\d{4}-[A-Z]+'
            ],
            'long': [
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}(-\d{2})?',
                r'\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+'
            ],
            'year': [
                r'\d{4}-\d{2}',
                r'\d{4}/\d{2}',
                r'\d{4}-\d{4}'
            ]
        }
        
        # Extract short codes
        for pattern in patterns['short']:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                code = match.group(0).strip()
                if not result['short_code']:
                    result['short_code'] = code
                    result['full_code'] = code
                result['all_codes'].append(code)
        
        # Extract long codes
        for pattern in patterns['long']:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                code = match.group(0).strip()
                if not result['long_code']:
                    result['long_code'] = code
                    if not result['full_code']:
                        result['full_code'] = code
                result['all_codes'].append(code)
        
        # Extract year
        all_codes_text = ' '.join(result['all_codes'])
        for pattern in patterns['year']:
            matches = re.finditer(pattern, all_codes_text)
            for match in matches:
                year = match.group(0).split('-')[0].split('/')[0]
                result['year'] = year
                break
            if result['year']:
                break
        
        return result
    except Exception as e:
        logger.error(f"Error extracting notification codes: {e}")
        return {
            'short_code': '',
            'long_code': '',
            'full_code': '',
            'year': '',
            'all_codes': []
        }

def parse_pdf_content(pdf_path: str, chunk_size: int = 8000) -> Tuple[List[Dict], Dict[str, str], str]:
    """
    Parse PDF content with enhanced metadata extraction.
    
    Args:
        pdf_path: Path to the PDF file
        chunk_size: Maximum characters per chunk
    
    Returns:
        Tuple containing:
        - List of content chunks with metadata
        - Dictionary of notification codes
        - Watermark status
    """
    try:
        # Check watermark status
        watermark_status = check_pdf_watermark(pdf_path)
        logger.info(f"📄 Watermark status: {watermark_status}")
        
        # Extract text content
        doc = fitz.open(pdf_path)
        chunks = []
        all_text = []
        
        for page_num, page in enumerate(doc, 1):
            # Get page text with formatting info
            blocks = page.get_text("dict")["blocks"]
            page_text = []
            current_chunk = {
                'content': '',
                'page_number': page_num,
                'positions': [],
                'formatting': []
            }
            
            for block in blocks:
                if block['type'] == 0:  # text block
                    for line in block['lines']:
                        for span in line['spans']:
                            text = span['text']
                            if text.strip():
                                current_chunk['content'] += text + ' '
                                current_chunk['positions'].append({
                                    'bbox': span['bbox'],
                                    'text': text
                                })
                                current_chunk['formatting'].append({
                                    'font': span['font'],
                                    'size': span['size'],
                                    'color': span['color']
                                })
                                page_text.append(text)
            
            # Add page text to all_text for code extraction
            all_text.extend(page_text)
            
            # Create chunk if it's full or end of page
            if len(current_chunk['content']) >= chunk_size or page_num == doc.page_count:
                if current_chunk['content'].strip():
                    chunks.append(current_chunk)
                current_chunk = {
                    'content': '',
                    'page_number': page_num,
                    'positions': [],
                    'formatting': []
                }
        
        doc.close()
        
        # Extract notification codes
        notification_codes = extract_notification_codes(' '.join(all_text))
        notification_codes['watermark_status'] = watermark_status
        notification_codes['is_active'] = watermark_status == "ACTIVE"
        
        logger.info(f"✅ Extracted {len(chunks)} chunks from PDF")
        logger.info(f"📋 Found codes: {notification_codes}")
        
        return chunks, notification_codes, watermark_status
    
    except Exception as e:
        logger.error(f"Error parsing PDF content: {e}")
        return [], {}, "UNKNOWN"
