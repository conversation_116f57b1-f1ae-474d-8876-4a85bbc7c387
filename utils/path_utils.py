import os
import sys
from pathlib import Path

def setup_project_paths():
    """Set up project paths and sys.path for imports"""
    try:
        # Get the project root directory (one level up from utils)
        project_root = Path(__file__).parent.parent.absolute()
        
        # Add project root and other important directories to sys.path
        paths_to_add = [
            str(project_root),
            str(project_root / 'complai_knowledge_tracker' / 'airflow' / 'dags'),
            str(project_root / 'utils'),
        ]
        
        # Add paths if they're not already in sys.path
        for path in paths_to_add:
            if path not in sys.path:
                sys.path.append(path)
        
        return True
        
    except Exception as e:
        print(f"Error setting up project paths: {e}")
        return False
