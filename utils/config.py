"""
Configuration module for Airflow DAGs
Centralizes all configuration, API keys, and environment variables
"""

import os
import itertools
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class OpenAIConfig:
    """OpenAI configuration settings"""
    api_keys: List[str]
    embedding_model: str = "text-embedding-3-large"
    chat_model: str = "gpt-4.1-mini-2025-04-14"
    nano_model: str = "gpt-4.1-nano-2025-04-14"
    max_retries: int = 5
    retry_min_seconds: int = 2
    retry_max_seconds: int = 4
    
    def __post_init__(self):
        self.key_cycle = itertools.cycle(self.api_keys)
    
    def get_next_key(self) -> str:
        """Get the next API key in rotation"""
        return next(self.key_cycle)

@dataclass
class QdrantConfig:
    """Qdrant vector database configuration"""
    host: str
    port: int = 6333
    url: Optional[str] = None
    collection_prefix: str = "rbi"
    embedding_size: int = 3072  # text-embedding-3-large dimension
    sparse_model: str = "Qdrant/bm25"
    splade_model: str = "naver/efficient-splade-VI-BT-large-doc"
    sparse_vector_name: str = "fast-sparse-bm25"
    splade_vector_name: str = "fast-sparse-bm25-splade"
    
    @property
    def full_url(self) -> str:
        """Get the full Qdrant URL"""
        if self.url:
            return self.url

        # Construct proper URL from host
        if self.host.startswith(('http://', 'https://')):
            return self.host
        else:
            return f"http://{self.host}:{self.port}"

@dataclass
class DatabaseConfig:
    """Database configuration for MongoDB/DocumentDB"""
    uri: str
    database_name: str = "rbi"
    collection_name: str = "rbi_regulations"
    rss_database_name: str = "rss_feed_db"
    rss_collection_name: str = "articles"
    use_tls: bool = True
    tls_ca_file: str = "/tmp/global-bundle.pem"
    replica_set: str = "rs0"
    read_preference: str = "secondaryPreferred"
    retry_writes: bool = False

@dataclass
class S3Config:
    """S3 configuration for file storage"""
    bucket_name: str
    access_key_id: str
    secret_access_key: str
    region: str = "us-east-1"

@dataclass
class ProcessingConfig:
    """Document processing configuration"""
    max_chunk_chars: int = 6000
    overlap_chars: int = 800
    max_summary_words: int = 150
    max_topics: int = 5
    batch_size: int = 1024
    max_concurrent_tasks: int = 8

class Config:
    """Main configuration class that loads all settings"""
    
    def __init__(self):
        self._load_config()
    
    def _load_config(self):
        """Load configuration from environment variables and sensible defaults"""
        try:
            # OpenAI Configuration - Check multiple possible environment variables
            openai_keys = []
            
            # Check for bulk keys first (OPENAI_API_KEYS)
            openai_keys_raw = os.environ.get("OPENAI_API_KEYS", "")
            if openai_keys_raw:
                openai_keys.extend([k.strip() for k in openai_keys_raw.split("|") if k.strip()])
            
            # Check for single key (OPENAI_API_KEY)
            single_key = os.environ.get("OPENAI_API_KEY", "")
            if single_key and single_key not in openai_keys:
                openai_keys.append(single_key)
                
            # Check for config file in home directory
            try:
                import json
                from pathlib import Path
                config_file = Path.home() / ".selkea" / "config.json"
                if config_file.exists():
                    with open(config_file, 'r') as f:
                        config_data = json.load(f)
                        file_key = config_data.get('OPENAI_API_KEY', '')
                        if file_key and file_key not in openai_keys:
                            openai_keys.append(file_key)
                            logger.info(f"Loaded API key from config file: {config_file}")
            except Exception as e:
                logger.warning(f"Could not load API key from config file: {e}")
            
            if not openai_keys:
                logger.error("No OpenAI API keys found in any location")
                logger.error("Please set your API key using one of these methods:")
                logger.error("1. Run the set_openai_key.py script: python set_openai_key.py YOUR_API_KEY")
                logger.error("2. Set the OPENAI_API_KEY environment variable: export OPENAI_API_KEY=your_api_key")
                logger.error("3. Create a config file at ~/.selkea/config.json with {'OPENAI_API_KEY': 'your_api_key'}")
                raise ValueError("No OpenAI API keys configured")

            self.openai = OpenAIConfig(api_keys=openai_keys)

            # Qdrant Configuration
            qdrant_host = os.environ.get("VECTOR_DB_HOST", "localhost")
            qdrant_url = os.environ.get("QDRANT_URL", None)

            # Clean up host value (remove any carriage returns or extra characters)
            if not qdrant_host:
                qdrant_host = "localhost"
            qdrant_host = qdrant_host.strip().replace('\r', '').replace('\n', '')
            if not qdrant_url:
                qdrant_url = None

            self.qdrant = QdrantConfig(
                host=qdrant_host,
                url=qdrant_url
            )

            # Database Configuration
            db_uri = os.environ.get("DOCUMENTDB_URI", "mongodb://localhost:27017/")
            if not db_uri:
                db_uri = "mongodb://localhost:27017/"
            self.database = DatabaseConfig(uri=db_uri)

            # S3 Configuration
            s3_bucket = "localdocdump"
            s3_access_key = "********************"
            s3_secret_key = "huMB0J5q7s3ImyOqTH3G5FTKSBoBIqkWCnCBuqKm"

            self.s3 = S3Config(
                bucket_name=s3_bucket,
                access_key_id=s3_access_key,
                secret_access_key=s3_secret_key
            )

            # Processing Configuration
            self.processing = ProcessingConfig()

            logger.info("Configuration loaded successfully")

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise

    def validate(self) -> bool:
        """Validate that all required configuration is present"""
        errors = []
        
        if not self.openai.api_keys:
            errors.append("OpenAI API keys not configured")
        
        if not self.s3.bucket_name:
            errors.append("S3 bucket name not configured")
        
        if not self.s3.access_key_id or not self.s3.secret_access_key:
            errors.append("S3 credentials not configured")
        
        if not self.database.uri:
            errors.append("Database URI not configured")
        
        if errors:
            logger.error(f"Configuration validation failed: {', '.join(errors)}")
            return False
        
        return True

# Global configuration instance
config = Config()
