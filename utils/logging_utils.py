"""
Utility functions for logging configuration and management.
Ensures logs are properly flushed to files and consistent across the application.
"""

import logging
import os
from datetime import datetime
import sys
import atexit

def setup_logging(log_dir="logs", log_level=logging.INFO, module_name=None,
                 include_timestamp=True, flush_interval=1):
    """
    Configure logging with proper file and console handlers ensuring logs get flushed.
    
    Args:
        log_dir (str): Directory to store log files
        log_level (int): Logging level (default: logging.INFO)
        module_name (str): Optional module name to include in log file name
        include_timestamp (bool): Whether to include timestamp in log file name
        flush_interval (int): How often to flush logs (in seconds)
    
    Returns:
        logger: Configured logger instance
    """
    # Create log directory if it doesn't exist
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Generate log file name
    timestamp = f"_{datetime.now().strftime('%Y%m%d_%H%M%S')}" if include_timestamp else ""
    module_prefix = f"{module_name}_" if module_name else ""
    log_file = os.path.join(log_dir, f"{module_prefix}execution{timestamp}.log")
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    
    # Create file handler with immediate flush
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    
    # Configure root logger if it hasn't been configured already
    if not logging.root.handlers:
        logging.basicConfig(level=log_level, handlers=[console_handler, file_handler])
    
    # Get or create logger for the module
    logger_name = module_name if module_name else __name__
    logger = logging.getLogger(logger_name)
    
    # Add handlers if they don't exist
    has_console_handler = any(isinstance(h, logging.StreamHandler) and 
                             not isinstance(h, logging.FileHandler) for h in logger.handlers)
    has_file_handler = any(isinstance(h, logging.FileHandler) for h in logger.handlers)
    
    if not has_console_handler:
        logger.addHandler(console_handler)
    
    if not has_file_handler:
        logger.addHandler(file_handler)
    
    # Set logger level
    logger.setLevel(log_level)
    
    # Ensure propagation is enabled
    logger.propagate = True
    
    # Set up periodic flushing
    if flush_interval > 0:
        import threading
        
        def flush_handlers():
            for handler in logger.handlers:
                handler.flush()
            threading.Timer(flush_interval, flush_handlers).start()
        
        flush_thread = threading.Timer(flush_interval, flush_handlers)
        flush_thread.daemon = True
        flush_thread.start()
    
    # Register flush on exit
    def flush_on_exit():
        for handler in logger.handlers:
            handler.flush()
            handler.close()
    
    atexit.register(flush_on_exit)
    
    # Log confirmation
    logger.info(f"Logger configured to write logs to: {log_file}")
    
    return logger

def force_flush_logs():
    """
    Explicitly flush all log handlers to ensure logs are written to disk.
    Call this function at critical points or before program exit.
    """
    for logger in [logging.getLogger(name) for name in logging.root.manager.loggerDict]:
        for handler in logger.handlers:
            handler.flush()
            
    # Also flush root logger handlers
    for handler in logging.root.handlers:
        handler.flush()
