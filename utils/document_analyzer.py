"""
Document relationship and supersession analysis for RBI notifications.
"""

import re
import logging
from typing import Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class DocumentRelationshipAnalyzer:
    """Analyzes relationships between documents and detects supersessions."""
    
    def __init__(self):
        self.relationship_patterns = {
            'supersedes': [
                r'supersedes?(?:\s+the)?\s+([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)',
                r'in\s+supersession\s+of\s+([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)',
                r'hereby\s+repeals?\s+([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)'
            ],
            'updates': [
                r'updates?(?:\s+the)?\s+([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)',
                r'revises?\s+(?:the\s+)?([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)'
            ],
            'references': [
                r'refers?\s+to\s+([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)',
                r'with\s+reference\s+to\s+([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)',
                r'as\s+per\s+([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)'
            ],
            'consolidates': [
                r'consolidates?\s+(?:the\s+)?([^,\.\n]+(?:circulars?|directions?|notifications?)[^,\.\n]*)',
                r'consolidation\s+of\s+([^,\.\n]+(?:circulars?|directions?|notifications?)[^,\.\n]*)'
            ]
        }
        
        self.doc_patterns = {
            'rbi_code': r'RBI/\d{4}(?:-\d{2})?/\d+',
            'circular': r'Circular\s+No\.\s*[A-Z]{2,4}\.[A-Z]{2,4}\.[A-Z]{1,4}\.\w+/\d+',
            'master_circular': r'Master\s+Circular\s+No\.\s*[A-Z]{2,4}\.[A-Z]{2,4}\.[A-Z]{1,4}\.\w+/\d+',
            'master_direction': r'Master\s+Direction\s+No\.\s*[A-Z]{2,4}\.[A-Z]{2,4}\.[A-Z]{1,4}\.\w+/\d+',
            'ap_series': r'A\.P\.\s*\(DIR\s*Series\)\s*Circular\s*No\.\s*\d+'
        }
    
    def analyze_relationships(self, content: str, current_codes: Dict[str, str]) -> List[Dict]:
        """
        Analyze document relationships in content.
        
        Args:
            content: The text content to analyze
            current_codes: Notification codes of the current document
        
        Returns:
            List of detected relationships
        """
        relationships = []
        
        # Extract all potential document references
        doc_refs = self._extract_document_references(content)
        
        # Check for new document creation
        new_doc_patterns = [
            r'(?:inclusion|addition)\s+of\s+"([^"]+)"\s+(?:in|to)\s+(?:the\s+)?([^,\.\n]+)',
            r'(?:inclusion|addition)\s+of\s+([^,]+(?:Bank|Institution)[^,\.\n]*)\s+(?:in|to)\s+(?:the\s+)?([^,\.\n]+)',
            r'(?:new|revised)\s+(?:guidelines|framework|norms)\s+(?:for|on)\s+([^,\.\n]+)',
            r'(?:establishment|creation)\s+of\s+([^,\.\n]+)'
        ]
        
        for pattern in new_doc_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) == 2:
                    entity, collection = match.groups()
                else:
                    entity = match.group(1)
                    collection = "General Guidelines"
                    
                relationships.append({
                    'type': 'creates',
                    'document': entity.strip(),
                    'collection': collection.strip(),
                    'confidence': 'HIGH',
                    'evidence': match.group(0)
                })
        
        # Regular document relationships
        for doc_ref in doc_refs:
            relationship = self._determine_relationship(content, doc_ref, current_codes)
            if relationship:
                relationships.append(relationship)
        
        # Check for document removal/cancellation
        removal_patterns = [
            r'(?:removal|deletion|exclusion)\s+of\s+"([^"]+)"\s+from\s+(?:the\s+)?([^,\.\n]+)',
            r'(?:removal|deletion|exclusion)\s+of\s+([^,]+(?:Bank|Institution)[^,\.\n]*)\s+from\s+(?:the\s+)?([^,\.\n]+)',
            r'(?:cancel|revoke|withdraw)\s+(?:the\s+)?([^,\.\n]+)'
        ]
        
        for pattern in removal_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                if len(match.groups()) == 2:
                    entity, collection = match.groups()
                else:
                    entity = match.group(1)
                    collection = "Unknown"
                    
                relationships.append({
                    'type': 'removes',
                    'document': entity.strip(),
                    'collection': collection.strip(),
                    'confidence': 'HIGH',
                    'evidence': match.group(0)
                })
        
        return relationships
    
    def detect_supersessions(self, content: str, notification_data: Dict) -> List[Dict]:
        """
        Detect document supersessions with high confidence.
        
        Args:
            content: The text content to analyze
            notification_data: Current notification metadata
        
        Returns:
            List of detected supersessions
        """
        supersessions = []
        current_year = notification_data.get('year', '')
        
        # Direct supersession statements
        for pattern in self.relationship_patterns['supersedes']:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                superseded_doc = match.group(1).strip()
                # Check if the superseded document has a year
                year_match = re.search(r'\b\d{4}\b', superseded_doc)
                supersessions.append({
                    'superseded_doc': superseded_doc,
                    'supersession_year': year_match.group(0) if year_match else current_year,
                    'confidence': 'HIGH',
                    'evidence': match.group(0),
                    'type': 'explicit_supersession',
                    'impact': 'full_supersession'
                })
        
        # Version-based supersession
        if current_year:
            year_pattern = r'(?:circular|direction|notification)[^\n]*(\d{4}(?:-\d{2})?)'
            matches = re.finditer(year_pattern, content, re.IGNORECASE)
            for match in matches:
                doc_year = match.group(1).split('-')[0]
                if doc_year < current_year:
                    supersessions.append({
                        'superseded_doc': match.group(0).strip(),
                        'confidence': 'MEDIUM',
                        'evidence': f"Newer version ({current_year}) supersedes older version ({doc_year})",
                        'type': 'version_supersession'
                    })
        
        return supersessions
    
    def _extract_document_references(self, content: str) -> List[str]:
        """Extract all document references from content."""
        references = set()
        
        for pattern in self.doc_patterns.values():
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                references.add(match.group(0).strip())
        
        # Also look for quoted titles
        quoted_titles = re.finditer(r'["\'](.*?(?:circular|direction|notification).*?)["\']', content, re.IGNORECASE)
        for match in quoted_titles:
            references.add(match.group(1).strip())
        
        return list(references)
    
    def _determine_relationship(self, content: str, doc_ref: str, current_codes: Dict[str, str]) -> Optional[Dict]:
        """Determine relationship type for a document reference."""
        
        # Look for relationship indicators around the reference
        context_window = 200
        doc_pos = content.lower().find(doc_ref.lower())
        if doc_pos == -1:
            return None
        
        start = max(0, doc_pos - context_window)
        end = min(len(content), doc_pos + len(doc_ref) + context_window)
        context = content[start:end]
        
        for rel_type, patterns in self.relationship_patterns.items():
            for pattern in patterns:
                if re.search(pattern.replace(r'([^,\.\n]+(?:circular|direction|notification)[^,\.\n]*)', re.escape(doc_ref)), context, re.IGNORECASE):
                    return {
                        'document': doc_ref,
                        'relationship': rel_type,
                        'confidence': 'HIGH',
                        'context': context.strip(),
                        'current_document': current_codes.get('full_code', '')
                    }
        
        # If no clear relationship found, return reference relationship
        return {
            'document': doc_ref,
            'relationship': 'references',
            'confidence': 'MEDIUM',
            'context': context.strip(),
            'current_document': current_codes.get('full_code', '')
        }
