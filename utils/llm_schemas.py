"""
Schema definitions for structured LLM outputs in the updation pipeline using Pydantic models
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum
import json

# Import the base enums from notification_categorizer
from prompts.notification_categorizer import NotificationCategory, ConfidenceLevel, ActionType

# Utility function to convert Pydantic schema to OpenAI schema format
def convert_pydantic_to_openai_schema(schema: Dict) -> Dict:
    """
    Convert a Pydantic schema to a format compatible with OpenAI structured output.
    Handles enum fields appropriately.
    """
    # Process enum fields to ensure they appear correctly in the schema
    props = schema.get("properties", {})
    for prop_name, prop_schema in props.items():
        # Handle enum fields to make sure they're presented correctly
        if prop_schema.get("allOf") and len(prop_schema["allOf"]) > 0:
            enum_ref = prop_schema["allOf"][0].get("$ref", "")
            if enum_ref and "#/definitions/" in enum_ref:
                enum_name = enum_ref.split("/")[-1]
                if enum_name in schema.get("definitions", {}):
                    enum_def = schema["definitions"][enum_name]
                    # Replace allOf with enum values
                    props[prop_name] = {
                        "type": "string",
                        "enum": enum_def.get("enum", []),
                        "description": prop_schema.get("description", "")
                    }
    
    return {
        "type": "object",
        "properties": props,
        "required": schema.get("required", [])
    }


class DocumentClassification(str, Enum):
    """Document classification categories"""
    REGULATORY = "regulatory"
    INFORMATIONAL = "informational"
    ADMINISTRATIVE = "administrative"
    WITHDRAWN = "withdrawn"


class CollectionName(str, Enum):
    """Target collection names in Qdrant"""
    RBI_CIRCULAR = "rbi_circular"
    RBI_MASTER_CIRCULAR = "rbi_master_circular"
    RBI_MASTER_DIRECTION = "rbi_master_direction"
    RBI_OTHER = "rbi_other"


class ProcessingType(str, Enum):
    """Types of processing needed for notifications"""
    STORE_NEW = "store_new"
    UPDATE_EXISTING = "update_existing"
    REMOVE_EXISTING = "remove_existing"
    NO_ACTION = "no_action"


# Model for document classification decisions
class DocumentClassificationModel(BaseModel):
    """Model for document classification decisions"""
    classification: DocumentClassification = Field(..., description="The classification category of this document")
    should_index: bool = Field(..., description="Whether this document should be indexed to the knowledge base")
    confidence: ConfidenceLevel = Field(..., description="Confidence level in this classification decision")
    target_collection: CollectionName = Field(..., description="The appropriate collection for storing this document")
    reasoning: str = Field(..., description="Brief explanation of the decision")
    
    # Method to generate JSON schema for OpenAI structured output
    @classmethod
    def get_json_schema(cls) -> Dict:
        schema = cls.schema()
        # Convert to OpenAI's expected format using our utility function
        return convert_pydantic_to_openai_schema(schema)


# Model for knowledge base action decisions
class KBActionModel(BaseModel):
    """Model for knowledge base action decisions"""
    action_type: ActionType = Field(..., description="The type of action to take for this document")
    classification: DocumentClassification = Field(..., description="The classification category of this document")
    should_store: bool = Field(..., description="Whether this document should be stored in the knowledge base")
    should_remove: bool = Field(..., description="Whether this document should be removed from the knowledge base")
    confidence: ConfidenceLevel = Field(..., description="Confidence level in this decision")
    target_collection: CollectionName = Field(..., description="The appropriate collection for this document")
    reasoning: str = Field(..., description="Brief explanation of the decision")
    
    @classmethod
    def get_json_schema(cls) -> Dict:
        schema = cls.schema()
        # Convert to OpenAI's expected format using our utility function
        return convert_pydantic_to_openai_schema(schema)


# Model for document metadata
class DocumentMetadata(BaseModel):
    """Additional metadata for a notification"""
    document_id: Optional[str] = Field(None, description="Document identifier")
    document_number: Optional[str] = Field(None, description="Official document number")
    superseded_documents: Optional[List[str]] = Field(default_factory=list, description="List of superseded document IDs")
    related_documents: Optional[List[str]] = Field(default_factory=list, description="List of related document IDs")


# Model for notification evaluation decisions
class NotificationEvaluationModel(BaseModel):
    """Model for notification evaluation decisions"""
    should_process: bool = Field(..., description="Whether this notification requires processing")
    should_store: bool = Field(..., description="Whether this notification should be stored in the knowledge base")
    target_collection: CollectionName = Field(..., description="The collection where this notification should be stored")
    should_remove_existing: bool = Field(..., description="Whether existing documents should be removed/superseded")
    processing_type: ProcessingType = Field(..., description="Type of processing needed for this notification")
    reasoning: str = Field(..., description="Explanation for the decision")
    confidence: ConfidenceLevel = Field(..., description="Confidence level in this decision")
    metadata: Optional[DocumentMetadata] = Field(None, description="Additional metadata for this notification")
    
    @classmethod
    def get_json_schema(cls) -> Dict:
        schema = cls.schema()
        # Convert to OpenAI's expected format using our utility function
        return convert_pydantic_to_openai_schema(schema)


# Constants for easy access to schemas
DOCUMENT_CLASSIFICATION_SCHEMA = DocumentClassificationModel.get_json_schema()
KB_ACTION_SCHEMA = KBActionModel.get_json_schema()
NOTIFICATION_EVALUATION_SCHEMA = NotificationEvaluationModel.get_json_schema()
