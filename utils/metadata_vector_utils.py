"""
Utilities for searching and managing document metadata vectors
"""

import logging
from typing import List, Dict, Any, Optional

from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient

logger = logging.getLogger(__name__)

# Constants
DEFAULT_EMBEDDING_MODEL = "all-MiniLM-L6-v2"
QDRANT_URL = "http://localhost:6333"
DEFAULT_METADATA_COLLECTION = "metadata_vectors"

class MetadataVectorSearch:
    """Class for searching document metadata using vector embeddings"""
    
    def __init__(self, 
                 model_name: str = DEFAULT_EMBEDDING_MODEL,
                 qdrant_url: str = QDRANT_URL,
                 collection_name: str = DEFAULT_METADATA_COLLECTION):
        """
        Initialize the metadata vector search
        
        Args:
            model_name: SentenceTransformer model to use for embeddings
            qdrant_url: URL for the Qdrant server
            collection_name: Default collection name for metadata vectors
        """
        try:
            self.model = SentenceTransformer(model_name)
            self.client = QdrantClient(qdrant_url)
            self.collection_name = collection_name
            logger.info(f"Initialized MetadataVectorSearch with model {model_name} and collection {collection_name}")
        except Exception as e:
            logger.error(f"Failed to initialize MetadataVectorSearch: {e}")
            raise
    
    def search_named_vector(self, 
                           field: str, 
                           query_text: str, 
                           collection_name: str = None, 
                           top_k: int = 5) -> List[Dict]:
        """
        Search for documents using vector similarity on a named field
        
        Args:
            field: The field to search on (e.g., document_id, short_summary, document_number)
            query_text: The text to search for
            collection_name: Optional override for collection name
            top_k: Number of results to return
            
        Returns:
            List of search hits with payload and score
        """
        try:
            # Use default collection if not specified
            coll_name = collection_name or self.collection_name
            
            # Encode the query text
            query_embedding = self.model.encode(query_text).tolist()
            
            # Search in Qdrant
            logger.info(f"Searching {coll_name} for '{query_text}' in field '{field}'")
            hits = self.client.search(
                collection_name=coll_name,
                query_vector=(f"{field}_vec", query_embedding),
                limit=top_k,
                with_payload=True,
                with_vectors=False
            )
            
            logger.info(f"Found {len(hits)} results for metadata search")
            return hits
            
        except Exception as e:
            logger.error(f"Error in metadata vector search: {e}")
            return []
    
    def search_document_by_id(self, document_id: str, top_k: int = 5) -> List[Dict]:
        """
        Search for documents by ID using vector similarity
        
        Args:
            document_id: The document ID to search for
            top_k: Number of results to return
            
        Returns:
            List of search hits
        """
        return self.search_named_vector("document_id", document_id, top_k=top_k)
    
    def search_document_by_number(self, document_number: str, top_k: int = 5) -> List[Dict]:
        """
        Search for documents by document number using vector similarity
        
        Args:
            document_number: The document number to search for
            top_k: Number of results to return
            
        Returns:
            List of search hits
        """
        return self.search_named_vector("document_number", document_number, top_k=top_k)
    
    def search_document_by_summary(self, summary_text: str, top_k: int = 5) -> List[Dict]:
        """
        Search for documents by summary text using vector similarity
        
        Args:
            summary_text: The summary text to search for
            top_k: Number of results to return
            
        Returns:
            List of search hits
        """
        return self.search_named_vector("short_summary", summary_text, top_k=top_k)
