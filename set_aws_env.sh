#!/bin/bash

# AWS Credentials
export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="huMB0J5q7s3ImyOqTH3G5FTKSBoBIqkWCnCBuqKm"
export S3_BUCKET_NAME="localdocdump"
export OPENAI_API_KEYS="********************************************************************************************************************************************************************|********************************************************************************************************************************************************************|********************************************************************************************************************************************************************"

# Echo confirmation
echo "AWS environment variables set successfully!"
echo "S3 bucket: $S3_BUCKET_NAME"
