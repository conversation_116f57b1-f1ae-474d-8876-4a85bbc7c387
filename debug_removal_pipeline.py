#!/usr/bin/env python3
"""
Debug script to trace why removal actions are not being performed.
This script will run through the pipeline and show exactly what's happening.
"""

import os
import sys
import json
import logging
from datetime import datetime
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_removal_pipeline.log')
    ]
)
logger = logging.getLogger(__name__)

def test_notification_analysis():
    """Test notification analysis to see if REMOVE_DOCUMENT actions are generated"""
    logger.info("🔍 Testing notification analysis for removal action generation...")
    
    # Set up environment
    os.environ['OPENAI_API_KEYS'] = os.getenv('OPENAI_API_KEYS', 'test-key')
    
    try:
        from manual_test import RealPipelineTest
        
        # Create test instance
        test_instance = RealPipelineTest()
        
        # Mock notification that should generate removal actions
        mock_notification = {
            'Title': 'Amendment to Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15',
            'Description': '''
            This circular amends the Guidelines on Risk Management and Inter-Bank Dealings.
            
            The earlier circular RBI/FED/2023-24/15 dated March 15, 2023 is hereby superseded.
            
            All banks are advised to follow the revised guidelines with immediate effect.
            ''',
            'Link': 'https://rbi.org.in/test',
            'Date': '2024-01-15'
        }
        
        logger.info(f"📋 Testing with notification: {mock_notification['Title']}")
        
        # Test notification analysis
        logger.info("🔍 Step 1: Analyzing notification...")
        analysis_result = test_instance.notification_processor.analyze_notification(
            mock_notification['Title'],
            mock_notification['Description'],
            mock_notification['Link']
        )
        
        logger.info(f"📊 Analysis result: {json.dumps(analysis_result, indent=2)}")
        
        # Test affected documents extraction
        logger.info("🔍 Step 2: Extracting affected documents...")
        affected_docs = test_instance.notification_processor.extract_affected_documents(
            mock_notification['Title'],
            mock_notification['Description'],
            analysis_result.get('category', 'regulatory')
        )
        
        logger.info(f"📊 Affected documents: {json.dumps(affected_docs, indent=2)}")
        
        # Check for removal actions
        document_actions = affected_docs.get('document_actions', [])
        removal_actions = [action for action in document_actions if action.get('action_type') == 'REMOVE_DOCUMENT']
        
        logger.info(f"🎯 Found {len(removal_actions)} REMOVE_DOCUMENT actions:")
        for i, action in enumerate(removal_actions, 1):
            logger.info(f"   {i}. Target: {action.get('target_document', 'N/A')}")
            logger.info(f"      Reasoning: {action.get('reasoning', 'N/A')}")
        
        # Test update actions determination
        logger.info("🔍 Step 3: Determining update actions...")
        update_actions = test_instance.notification_processor.determine_update_actions(
            mock_notification['Title'],
            analysis_result.get('category', 'regulatory'),
            document_actions,
            mock_notification['Description']
        )
        
        logger.info(f"📊 Update actions: {json.dumps(update_actions, indent=2)}")
        
        # Check for removal actions in update actions
        update_removal_actions = [action for action in update_actions.get('actions', []) if action.get('action_type') == 'REMOVE_DOCUMENT']
        
        logger.info(f"🎯 Found {len(update_removal_actions)} REMOVE_DOCUMENT actions in update actions:")
        for i, action in enumerate(update_removal_actions, 1):
            logger.info(f"   {i}. Target: {action.get('target_document', 'N/A')}")
            logger.info(f"      Details: {action.get('details', 'N/A')}")
        
        # Summary
        total_removal_actions = len(removal_actions) + len(update_removal_actions)
        if total_removal_actions > 0:
            logger.info(f"✅ SUCCESS: Found {total_removal_actions} total removal actions")
            return True
        else:
            logger.warning("⚠️ NO REMOVAL ACTIONS GENERATED - This might be the issue!")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in notification analysis test: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_kb_execution():
    """Test KB execution with a mock removal action"""
    logger.info("🔍 Testing KB execution with mock removal action...")
    
    try:
        from utils.knowledge_base_update_executor import KnowledgeBaseUpdateExecutor
        
        executor = KnowledgeBaseUpdateExecutor()
        
        # Create a realistic removal action
        removal_action = {
            'action_type': 'REMOVE_DOCUMENT',
            'target_document': 'RBI/FED/2023-24/15',
            'collection_name': ['rbi_circular'],
            'use_vector_search': True,
            'document_id': 'RBI/FED/2023-24/15',
            'reasoning': 'Superseded by new circular'
        }
        
        notification_data = {
            'Title': 'Amendment to Guidelines on Risk Management',
            'Date': '2024-01-15'
        }
        
        logger.info(f"🎯 Executing removal action: {removal_action}")
        
        # Execute the removal
        results = executor.execute_updates([removal_action], notification_data)
        
        logger.info(f"📊 Execution results: {json.dumps(results, indent=2, default=str)}")
        
        if results and len(results) > 0:
            result = results[0]
            if result.get('success'):
                logger.info("✅ Removal action executed successfully")
                return True
            else:
                logger.warning(f"⚠️ Removal action failed: {result.get('details', 'Unknown error')}")
                return False
        else:
            logger.error("❌ No results returned from executor")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error in KB execution test: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_end_to_end():
    """Test end-to-end removal action flow"""
    logger.info("🔍 Testing end-to-end removal action flow...")
    
    try:
        from manual_test import RealPipelineTest
        
        # Create test instance
        test_instance = RealPipelineTest()
        
        # Mock notification with clear supersession
        mock_notification = {
            'Title': 'Revised Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15',
            'Description': '''
            In supersession of the circular RBI/FED/2023-24/15 dated March 15, 2023, 
            the Reserve Bank issues the following revised guidelines on Risk Management.
            
            The earlier circular RBI/FED/2023-24/15 is hereby withdrawn.
            ''',
            'Link': 'https://rbi.org.in/test',
            'Date': '2024-01-15'
        }
        
        logger.info(f"📋 Testing end-to-end with: {mock_notification['Title']}")
        
        # Process the notification (this should generate and execute removal actions)
        result = test_instance.process_notification_enhanced(mock_notification)
        
        logger.info(f"📊 Processing result keys: {list(result.keys())}")
        
        # Check for removal actions in the result
        if 'kb_actions' in result:
            kb_actions = result['kb_actions']
            removal_actions = [action for action in kb_actions if action.get('action_type') == 'REMOVE_DOCUMENT']
            logger.info(f"🎯 Found {len(removal_actions)} removal actions in KB actions")
        
        if 'affected_documents_result' in result:
            affected_docs = result['affected_documents_result']
            if 'document_actions' in affected_docs:
                doc_actions = affected_docs['document_actions']
                removal_actions = [action for action in doc_actions if action.get('action_type') == 'REMOVE_DOCUMENT']
                logger.info(f"🎯 Found {len(removal_actions)} removal actions in affected documents")
        
        # Check execution results
        if 'kb_execution_results' in result:
            kb_results = result['kb_execution_results']
            removal_results = [r for r in kb_results if r.get('action') == 'REMOVE_DOCUMENT']
            logger.info(f"🎯 Found {len(removal_results)} removal execution results")
            
            for i, removal_result in enumerate(removal_results, 1):
                success = removal_result.get('success', False)
                details = removal_result.get('details', 'N/A')
                logger.info(f"   {i}. Success: {success}, Details: {details}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error in end-to-end test: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """Run all debug tests"""
    logger.info("🚀 Starting removal action debugging...")
    logger.info("=" * 60)
    
    tests = [
        ("Notification Analysis", test_notification_analysis),
        ("KB Execution", test_kb_execution),
        ("End-to-End Flow", test_end_to_end),
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        logger.info("-" * 40)
        try:
            result = test_func()
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status} {test_name}")
        except Exception as e:
            logger.error(f"❌ FAILED {test_name}: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info("🎯 Debug session complete. Check logs for details.")

if __name__ == "__main__":
    main()
