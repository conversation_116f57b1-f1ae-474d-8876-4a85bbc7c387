#!/usr/bin/env python3
"""
Quick test to verify key pipeline enhancements are working
"""

import logging
import sys
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_enhanced_document_extractor():
    """Test enhanced document extractor"""
    logger.info("🧪 Testing Enhanced Document Extractor")
    
    try:
        from utils.enhanced_document_extractor import EnhancedDocumentExtractor
        
        extractor = EnhancedDocumentExtractor()
        
        # Test case 1: RBI format
        extraction1 = extractor.extract_document_id(
            title="Amendment to Guidelines - RBI/FED/2024-25/17",
            content="This circular amends existing guidelines..."
        )
        
        # Test case 2: DBR format
        extraction2 = extractor.extract_document_id(
            title="Master Direction on Credit Risk",
            content="DBR.No.Ret.BC.78/12.02.001/2024-25 supersedes previous directions..."
        )
        
        # Test case 3: Circular format
        extraction3 = extractor.extract_document_id(
            title="Banking Operations Circular",
            content="FIDD.CO.Plan.BC.No.4/04.09.01/2024-25 provides guidance..."
        )
        
        results = [extraction1, extraction2, extraction3]
        passed = sum(1 for r in results if r.confidence > 0.7)
        
        logger.info(f"📊 Document Extractor: {passed}/3 tests passed")
        for i, result in enumerate(results, 1):
            logger.info(f"   Test {i}: {result.document_id} (confidence: {result.confidence:.3f})")
        
        return passed >= 2
        
    except Exception as e:
        logger.error(f"❌ Document extractor test failed: {e}")
        return False

def test_manual_review_system():
    """Test manual review system"""
    logger.info("🧪 Testing Manual Review System")
    
    try:
        from manual_review_system import ManualReviewSystem
        
        review_system = ManualReviewSystem(review_dir="test_quick_reviews")
        
        # Test creating a review item
        test_notification = {
            'Title': 'Test Enhancement Notification',
            'Link': 'https://example.com/test',
            'content': 'Test content for quick verification'
        }
        
        review_id = review_system.create_inexact_match_review(
            action_type="UPDATE_DOCUMENT",
            notification_data=test_notification,
            target_document="RBI/TEST/2024-25/01",
            confidence_score=0.45
        )
        
        # Test summary
        summary = review_system.get_review_summary()
        
        success = bool(review_id and summary['total_reviews'] > 0)
        
        if success:
            logger.info(f"   ✅ Created review: {review_id}")
            logger.info(f"   ✅ Summary: {summary['total_reviews']} total reviews")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Manual review test failed: {e}")
        return False

def test_validation_guard_rails():
    """Test basic validation logic"""
    logger.info("🧪 Testing Validation Guard-rails")
    
    try:
        # Test empty target document validation
        test_action = {
            'action_type': 'UPDATE_DOCUMENT',
            'target_document': '',  # Empty target
        }
        
        test_notification = {
            'Title': 'Test Validation',
            'content': 'Test content'
        }
        
        # Import and test validation
        from utils.knowledge_base_update_executor import KnowledgeBaseUpdateExecutor
        executor = KnowledgeBaseUpdateExecutor()
        
        validation = executor._validate_action_pre_execution(test_action, test_notification)
        
        # Should fail validation for empty target
        empty_target_handled = not validation['valid'] and 'Missing target document' in validation['reason']
        
        if empty_target_handled:
            logger.info("   ✅ Empty target document properly rejected")
        else:
            logger.warning("   ❌ Empty target document not properly handled")
        
        return empty_target_handled
        
    except Exception as e:
        logger.error(f"❌ Validation test failed: {e}")
        return False

def main():
    """Run quick enhancement tests"""
    logger.info("🚀 Running Quick Enhancement Tests")
    
    test_results = {
        'document_extractor': test_enhanced_document_extractor(),
        'manual_review': test_manual_review_system(),
        'validation': test_validation_guard_rails()
    }
    
    # Calculate success rate
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = passed_tests / total_tests
    
    logger.info(f"\n📊 QUICK TEST SUMMARY:")
    logger.info(f"   Overall Success Rate: {success_rate:.1%} ({passed_tests}/{total_tests})")
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"   {test_name.replace('_', ' ').title()}: {status}")
    
    if success_rate >= 0.7:
        logger.info("\n🎉 Key enhancements are working!")
        logger.info("   • Enhanced document extraction is functional")
        logger.info("   • Manual review system is operational")
        logger.info("   • Basic validation guard-rails are in place")
        logger.info("\n✅ Pipeline enhancements successfully implemented")
    else:
        logger.info("\n🔧 Some enhancements need attention:")
        failed_tests = [name for name, result in test_results.items() if not result]
        for test in failed_tests:
            logger.info(f"   • Fix {test.replace('_', ' ')}")
    
    return success_rate >= 0.7

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
