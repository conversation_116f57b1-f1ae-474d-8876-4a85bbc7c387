#!/usr/bin/env python3
"""
Manual Database Addition Tool for Adding Documents to Qdrant Collections
"""

import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.qdrant_utils import QdrantManager
from utils.config import Config
from manual_review_system import ManualReviewSystem, ManualAdditionRequest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ManualDatabaseAdder:
    """Tool for manually adding documents to the database"""
    
    def __init__(self):
        """Initialize the manual database adder"""
        try:
            # Load configuration
            self.config = Config()
            
            # Initialize Qdrant manager
            self.qdrant_manager = QdrantManager()
            
            # Initialize review system
            self.review_system = ManualReviewSystem()
            
            logger.info("✅ Manual Database Adder initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Manual Database Adder: {e}")
            raise
    
    def add_document_from_request(self, request_id: str) -> bool:
        """
        Add a document to the database from a manual addition request
        
        Args:
            request_id: ID of the manual addition request
            
        Returns:
            bool: True if successful
        """
        try:
            # Load the addition request
            additions = self.review_system._load_additions()
            request = None
            
            for addition in additions:
                if addition['request_id'] == request_id:
                    request = addition
                    break
            
            if not request:
                logger.error(f"❌ Addition request not found: {request_id}")
                return False
            
            if request['status'] != 'PENDING':
                logger.warning(f"⚠️ Request {request_id} is not pending (status: {request['status']})")
                return False
            
            # Add the document
            success = self._add_document_to_collection(
                document_id=request['document_id'],
                title=request['document_title'],
                content=request['document_content'],
                collection=request['target_collection'],
                document_type=request['document_type'],
                metadata=request['metadata']
            )
            
            # Update request status
            if success:
                request['status'] = 'COMPLETED'
                request['processing_notes'] = f"Successfully added to {request['target_collection']} at {datetime.now().isoformat()}"
            else:
                request['status'] = 'FAILED'
                request['processing_notes'] = f"Failed to add to database at {datetime.now().isoformat()}"
            
            # Save updated requests
            self.review_system._save_additions(additions)
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error adding document from request: {e}")
            return False
    
    def add_document_direct(self, document_id: str, title: str, content: str,
                          collection: str, document_type: str = "manual",
                          metadata: Optional[Dict] = None) -> bool:
        """
        Directly add a document to the database
        
        Args:
            document_id: Unique document identifier
            title: Document title
            content: Document content
            collection: Target collection name
            document_type: Type of document
            metadata: Additional metadata
            
        Returns:
            bool: True if successful
        """
        try:
            if metadata is None:
                metadata = {}
            
            return self._add_document_to_collection(
                document_id=document_id,
                title=title,
                content=content,
                collection=collection,
                document_type=document_type,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"❌ Error adding document directly: {e}")
            return False
    
    def _add_document_to_collection(self, document_id: str, title: str, content: str,
                                  collection: str, document_type: str,
                                  metadata: Dict) -> bool:
        """
        Internal method to add document to Qdrant collection
        
        Args:
            document_id: Document identifier
            title: Document title
            content: Document content
            collection: Collection name
            document_type: Document type
            metadata: Document metadata
            
        Returns:
            bool: True if successful
        """
        try:
            logger.info(f"📝 Adding document {document_id} to collection {collection}")
            
            # Prepare document data
            document_data = {
                'document_id': document_id,
                'title': title,
                'content': content,
                'document_type': document_type,
                'source': 'manual_addition',
                'timestamp': datetime.now().isoformat(),
                **metadata
            }
            
            # Create chunks from content
            chunks = self._create_chunks(content, document_id, title)
            
            # Add each chunk to the collection
            success_count = 0
            for i, chunk in enumerate(chunks):
                chunk_id = f"{document_id}_chunk_{i}"
                
                # Prepare chunk payload
                chunk_payload = {
                    **document_data,
                    'chunk_id': chunk_id,
                    'chunk_index': i,
                    'chunk_content': chunk,
                    'chunk_length': len(chunk)
                }
                
                # Add to Qdrant
                try:
                    result = self.qdrant_manager.add_document_chunk(
                        collection_name=collection,
                        chunk_id=chunk_id,
                        chunk_content=chunk,
                        metadata=chunk_payload
                    )
                    
                    if result:
                        success_count += 1
                        logger.info(f"✅ Added chunk {i+1}/{len(chunks)} for document {document_id}")
                    else:
                        logger.error(f"❌ Failed to add chunk {i+1} for document {document_id}")
                        
                except Exception as chunk_error:
                    logger.error(f"❌ Error adding chunk {i+1}: {chunk_error}")
            
            # Check if all chunks were added successfully
            if success_count == len(chunks):
                logger.info(f"✅ Successfully added document {document_id} with {success_count} chunks to {collection}")
                return True
            else:
                logger.warning(f"⚠️ Partially added document {document_id}: {success_count}/{len(chunks)} chunks successful")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error adding document to collection: {e}")
            return False
    
    def _create_chunks(self, content: str, document_id: str, title: str,
                      max_chunk_size: int = 6000, overlap: int = 800) -> List[str]:
        """
        Create chunks from document content
        
        Args:
            content: Document content
            document_id: Document identifier
            title: Document title
            max_chunk_size: Maximum chunk size in characters
            overlap: Overlap between chunks
            
        Returns:
            List of content chunks
        """
        try:
            if len(content) <= max_chunk_size:
                return [content]
            
            chunks = []
            start = 0
            
            while start < len(content):
                end = start + max_chunk_size
                
                # If this is not the last chunk, try to break at a sentence boundary
                if end < len(content):
                    # Look for sentence endings within the last 200 characters
                    search_start = max(start + max_chunk_size - 200, start)
                    sentence_end = content.rfind('.', search_start, end)
                    
                    if sentence_end > start:
                        end = sentence_end + 1
                
                chunk = content[start:end].strip()
                if chunk:
                    chunks.append(chunk)
                
                # Move start position with overlap
                start = end - overlap if end < len(content) else end
            
            logger.info(f"📄 Created {len(chunks)} chunks for document {document_id}")
            return chunks
            
        except Exception as e:
            logger.error(f"❌ Error creating chunks: {e}")
            return [content]  # Return original content as single chunk
    
    def process_pending_additions(self) -> Dict[str, int]:
        """
        Process all pending manual addition requests
        
        Returns:
            Dict with success/failure counts
        """
        try:
            additions = self.review_system._load_additions()
            pending_additions = [a for a in additions if a['status'] == 'PENDING']
            
            results = {'success': 0, 'failed': 0, 'total': len(pending_additions)}
            
            logger.info(f"📋 Processing {len(pending_additions)} pending addition requests")
            
            for addition in pending_additions:
                success = self.add_document_from_request(addition['request_id'])
                if success:
                    results['success'] += 1
                    logger.info(f"✅ Processed: {addition['request_id']}")
                else:
                    results['failed'] += 1
                    logger.error(f"❌ Failed: {addition['request_id']}")
            
            logger.info(f"📊 Processing complete: {results['success']} success, {results['failed']} failed")
            return results
            
        except Exception as e:
            logger.error(f"❌ Error processing pending additions: {e}")
            return {'success': 0, 'failed': 0, 'total': 0}
    
    def list_collections(self) -> List[str]:
        """List available Qdrant collections"""
        try:
            collections = self.qdrant_manager.list_collections()
            logger.info(f"📋 Available collections: {collections}")
            return collections
        except Exception as e:
            logger.error(f"❌ Error listing collections: {e}")
            return []
    
    def validate_collection(self, collection_name: str) -> bool:
        """Validate if collection exists"""
        try:
            collections = self.list_collections()
            return collection_name in collections
        except Exception as e:
            logger.error(f"❌ Error validating collection: {e}")
            return False

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(description="Manual Database Addition Tool")
    parser.add_argument('--action', choices=['add', 'process', 'list'], required=True,
                       help='Action to perform')
    parser.add_argument('--request-id', help='Request ID for adding from request')
    parser.add_argument('--document-id', help='Document ID for direct addition')
    parser.add_argument('--title', help='Document title for direct addition')
    parser.add_argument('--content-file', help='File containing document content')
    parser.add_argument('--collection', help='Target collection name')
    parser.add_argument('--document-type', default='manual', help='Document type')
    
    args = parser.parse_args()
    
    try:
        adder = ManualDatabaseAdder()
        
        if args.action == 'list':
            collections = adder.list_collections()
            print(f"Available collections: {collections}")
            
        elif args.action == 'process':
            results = adder.process_pending_additions()
            print(f"Processing results: {results}")
            
        elif args.action == 'add':
            if args.request_id:
                success = adder.add_document_from_request(args.request_id)
                print(f"Addition from request: {'SUCCESS' if success else 'FAILED'}")
                
            elif args.document_id and args.title and args.content_file and args.collection:
                with open(args.content_file, 'r') as f:
                    content = f.read()
                
                success = adder.add_document_direct(
                    document_id=args.document_id,
                    title=args.title,
                    content=content,
                    collection=args.collection,
                    document_type=args.document_type
                )
                print(f"Direct addition: {'SUCCESS' if success else 'FAILED'}")
                
            else:
                print("Error: Missing required arguments for direct addition")
                parser.print_help()
        
    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
