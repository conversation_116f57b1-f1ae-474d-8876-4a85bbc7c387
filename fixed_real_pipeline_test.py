"""
Create a fixed version of real_pipeline_test.py
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from utils.openai_utils import MockOpenAIManager
from utils.config import StandaloneConfig
from prompts.notification_categorizer import AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealNotificationProcessor:
    """
    Class for processing RBI notifications
    """
    def __init__(self, openai_manager):
        self.openai_manager = openai_manager

class RealPipelineTest:
    """Test the real notification processing pipeline"""
    
    def __init__(self, notifications_file: str = "rbi_notifications.json"):
        self.notifications_file = Path(notifications_file)
        self.notifications = []
        self.results = []
        
        # Set up standalone configuration
        self.setup_environment()
        
        # Create the notification processor
        self.setup_notification_processor()
    
    def setup_environment(self):
        """Set up the environment for standalone testing"""
        try:
            # Check for OpenAI API key (support both OPENAI_API_KEYS and OPENAI_API_KEY)
            if not (os.getenv('OPENAI_API_KEYS') or os.getenv('OPENAI_API_KEY')):
                raise ValueError(
                    "OPENAI_API_KEYS or OPENAI_API_KEY environment variable is required. "
                    "Please set it with: export OPENAI_API_KEYS='key1|key2|key3' or export OPENAI_API_KEY='your-key-here'"
                )
            
            # Create standalone config
            self.config = StandaloneConfig()
            
            # Initialize the OpenAI manager with key rotation
            self.openai_manager = MockOpenAIManager(self.config.openai_api_keys)
            
            logger.info("✅ Environment setup complete")
            
        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            raise

    def setup_notification_processor(self):
        """Set up the real notification processor"""
        try:
            # Create the notification processor
            self.notification_processor = RealNotificationProcessor(self.openai_manager)
            logger.info("✅ Real notification processor initialized")
        except Exception as e:
            logger.error(f"❌ Failed to set up notification processor: {e}")
            raise

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Run the RBI notification pipeline test")
    parser.add_argument("--max", type=int, default=None, help="Maximum number of notifications to process")
    parser.add_argument("--start", type=int, default=0, help="Start index for notifications")
    
    args = parser.parse_args()
    
    print(f"Running pipeline test with max={args.max}, start={args.start}")
    
    # Create the pipeline test
    test = RealPipelineTest()
    
    # Load the affected documents extractor prompt
    print(f"AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT snippet: {AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT[:100]}...")

if __name__ == "__main__":
    main()
