#!/usr/bin/env python3
"""
Run All Pipeline Tests

This script runs all test categories and creates a master verification report.
"""

import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import subprocess

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_all_pipeline_tests():
    """Run all pipeline test categories"""
    
    # Check environment
    if not (os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_API_KEYS')):
        logger.error("❌ OPENAI_API_KEY or OPENAI_API_KEYS environment variable required")
        return
    
    # Find all test categories
    test_dir = Path("pipeline_tests")
    test_files = list(test_dir.glob("*.json"))
    
    if not test_files:
        logger.error("❌ No test files found")
        return
    
    logger.info(f"🚀 Running tests for {len(test_files)} categories")
    
    # Create master output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    master_output_dir = Path(f"master_test_results_{timestamp}")
    master_output_dir.mkdir(exist_ok=True)
    
    all_results = {}
    total_tests = 0
    total_successful = 0
    total_kb_actions = 0
    
    # Run each category
    for test_file in test_files:
        category = test_file.stem
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 Running category: {category}")
        
        try:
            # Run the simple test runner for this category
            result = subprocess.run([
                sys.executable, 'simple_test_runner.py', category, '2'
            ], capture_output=True, text=True, env=os.environ.copy())
            
            if result.returncode == 0:
                logger.info(f"✅ {category} completed successfully")
                
                # Find the output directory
                output_dirs = list(Path('.').glob(f"simple_test_outputs_{category}_*"))
                if output_dirs:
                    latest_dir = max(output_dirs, key=lambda p: p.stat().st_mtime)
                    
                    # Read summary
                    summary_file = latest_dir / "summary.json"
                    if summary_file.exists():
                        with open(summary_file, 'r') as f:
                            summary = json.load(f)
                        
                        all_results[category] = {
                            'status': 'success',
                            'summary': summary,
                            'output_dir': str(latest_dir)
                        }
                        
                        total_tests += summary.get('total_tests', 0)
                        total_successful += summary.get('successful_tests', 0)
                        total_kb_actions += summary.get('total_kb_actions', 0)
                    else:
                        all_results[category] = {'status': 'no_summary', 'output_dir': str(latest_dir)}
                else:
                    all_results[category] = {'status': 'no_output'}
            else:
                logger.error(f"❌ {category} failed: {result.stderr}")
                all_results[category] = {
                    'status': 'failed',
                    'error': result.stderr,
                    'stdout': result.stdout
                }
        
        except Exception as e:
            logger.error(f"❌ Error running {category}: {e}")
            all_results[category] = {'status': 'error', 'error': str(e)}
    
    # Create master summary
    master_summary = {
        'timestamp': datetime.now().isoformat(),
        'total_categories': len(test_files),
        'successful_categories': len([r for r in all_results.values() if r.get('status') == 'success']),
        'total_tests': total_tests,
        'total_successful': total_successful,
        'total_kb_actions': total_kb_actions,
        'categories': all_results
    }
    
    # Save master summary
    master_file = master_output_dir / "master_summary.json"
    with open(master_file, 'w', encoding='utf-8') as f:
        json.dump(master_summary, f, indent=2, ensure_ascii=False, default=str)
    
    # Create master verification report
    report_file = master_output_dir / "MASTER_VERIFICATION_REPORT.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# Master Pipeline Test Verification Report\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## Overall Summary\n\n")
        f.write(f"- **Total Categories:** {master_summary['total_categories']}\n")
        f.write(f"- **Successful Categories:** {master_summary['successful_categories']}\n")
        f.write(f"- **Total Tests:** {master_summary['total_tests']}\n")
        f.write(f"- **Successful Tests:** {master_summary['total_successful']}\n")
        f.write(f"- **Total KB Actions:** {master_summary['total_kb_actions']}\n\n")
        
        f.write("## Category Results\n\n")
        for category, result in all_results.items():
            if result.get('status') == 'success':
                summary = result.get('summary', {})
                f.write(f"### ✅ {category.title().replace('_', ' ')}\n")
                f.write(f"- **Tests:** {summary.get('successful_tests', 0)}/{summary.get('total_tests', 0)} successful\n")
                f.write(f"- **KB Actions:** {summary.get('total_kb_actions', 0)}\n")
                f.write(f"- **Output:** `{result.get('output_dir', 'N/A')}`\n\n")
            else:
                f.write(f"### ❌ {category.title().replace('_', ' ')} (FAILED)\n")
                f.write(f"- **Status:** {result.get('status', 'unknown')}\n")
                if result.get('error'):
                    f.write(f"- **Error:** {result.get('error', 'Unknown')[:200]}...\n")
                f.write("\n")
        
        f.write("## Manual Verification Instructions\n\n")
        f.write("1. **Review Individual Category Reports:**\n")
        f.write("   - Each successful category has its own output directory\n")
        f.write("   - Check the `VERIFICATION_REPORT.md` in each directory\n\n")
        
        f.write("2. **Verify KB Actions by Category:**\n")
        f.write("   - **Bank Rate Changes:** Should generate UPDATE_DOCUMENT actions\n")
        f.write("   - **Withdrawal Circulars:** Should generate REMOVE_DOCUMENT actions\n")
        f.write("   - **Appointments:** Should generate ADD_DOCUMENT actions\n")
        f.write("   - **Name Alterations:** Should generate UPDATE_DOCUMENT actions\n\n")
        
        f.write("3. **Check Action Consistency:**\n")
        f.write("   - Verify target documents are reasonable\n")
        f.write("   - Ensure collections are appropriate\n")
        f.write("   - Check priority assignments\n\n")
        
        f.write("## Test Validation Checklist\n\n")
        f.write("- [ ] All categories processed successfully\n")
        f.write("- [ ] KB actions match expected patterns\n")
        f.write("- [ ] Document identifiers are reasonable\n")
        f.write("- [ ] Collections are correctly assigned\n")
        f.write("- [ ] Priorities are appropriate\n")
        f.write("- [ ] No critical errors in processing\n")
    
    # Create consolidated CSV
    csv_file = master_output_dir / "consolidated_verification.csv"
    with open(csv_file, 'w', encoding='utf-8') as f:
        f.write("Category,Test_Count,Success_Count,KB_Actions,Status,Output_Dir\n")
        for category, result in all_results.items():
            if result.get('status') == 'success':
                summary = result.get('summary', {})
                f.write(f"{category},{summary.get('total_tests', 0)},{summary.get('successful_tests', 0)},{summary.get('total_kb_actions', 0)},SUCCESS,{result.get('output_dir', '')}\n")
            else:
                f.write(f"{category},0,0,0,FAILED,\n")
    
    logger.info(f"\n🎉 Master test run completed!")
    logger.info(f"📊 Overall: {master_summary['total_successful']}/{master_summary['total_tests']} tests successful")
    logger.info(f"📊 Categories: {master_summary['successful_categories']}/{master_summary['total_categories']} successful")
    logger.info(f"📊 Total KB Actions: {master_summary['total_kb_actions']}")
    logger.info(f"📁 Master results: {master_output_dir}")
    logger.info(f"📋 Review: {report_file}")

if __name__ == "__main__":
    run_all_pipeline_tests()